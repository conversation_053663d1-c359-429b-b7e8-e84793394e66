/**
 * Gallery functionality for masonry grid and modal display
 */

class Gallery {
    constructor() {
        this.modal = document.getElementById('gallery-modal');
        this.modalImage = document.getElementById('modal-image');
        this.modalCaption = document.getElementById('modal-caption');
        this.modalCategory = document.getElementById('modal-category');
        this.modalCommentary = document.getElementById('modal-commentary');
        this.modalClose = document.querySelector('.modal-close');
        this.modalPrev = document.getElementById('modal-prev');
        this.modalNext = document.getElementById('modal-next');
        this.galleryGrid = document.getElementById('gallery-grid');
        this.filterButtons = document.querySelectorAll('.filter-btn');
        
        this.currentImages = [];
        this.currentIndex = 0;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupFilters();
        this.updateCurrentImages();
    }
    
    setupEventListeners() {
        // Gallery item clicks
        this.galleryGrid.addEventListener('click', (e) => {
            const galleryItem = e.target.closest('.gallery-item');
            if (galleryItem) {
                const img = galleryItem.querySelector('img');
                this.openModal(img);
            }
        });
        
        // Modal close events
        this.modalClose.addEventListener('click', () => this.closeModal());
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.closeModal();
            }
        });
        
        // Modal navigation
        this.modalPrev.addEventListener('click', () => this.previousImage());
        this.modalNext.addEventListener('click', () => this.nextImage());
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (this.modal.classList.contains('show')) {
                switch(e.key) {
                    case 'Escape':
                        this.closeModal();
                        break;
                    case 'ArrowLeft':
                        this.previousImage();
                        break;
                    case 'ArrowRight':
                        this.nextImage();
                        break;
                }
            }
        });
    }
    
    setupFilters() {
        this.filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                this.filterButtons.forEach(btn => btn.classList.remove('active'));
                // Add active class to clicked button
                button.classList.add('active');
                
                const category = button.dataset.category;
                this.filterImages(category);
            });
        });
    }
    
    filterImages(category) {
        const galleryItems = document.querySelectorAll('.gallery-item');
        
        galleryItems.forEach(item => {
            if (category === 'all' || item.dataset.category === category) {
                item.style.display = 'block';
                // Add animation
                item.style.opacity = '0';
                item.style.transform = 'scale(0.8)';
                setTimeout(() => {
                    item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'scale(1)';
                }, 50);
            } else {
                item.style.display = 'none';
            }
        });
        
        // Update current images for modal navigation
        this.updateCurrentImages();
    }
    
    updateCurrentImages() {
        const visibleItems = document.querySelectorAll('.gallery-item[style*="display: block"], .gallery-item:not([style*="display: none"])');
        this.currentImages = Array.from(visibleItems).map(item => item.querySelector('img')).filter(img => img && img.src);
    }
    
    openModal(img) {
        this.modalImage.src = img.src;
        this.modalImage.alt = img.alt;
        this.modalCaption.textContent = img.dataset.caption;
        this.modalCategory.textContent = img.dataset.category;

        // Handle commentary
        const commentary = img.dataset.commentary;
        if (commentary && commentary.trim()) {
            this.modalCommentary.textContent = commentary;
            this.modalCommentary.style.display = 'block';
        } else {
            this.modalCommentary.style.display = 'none';
        }

        // Find current index
        this.currentIndex = this.currentImages.indexOf(img);

        // Show modal
        this.modal.classList.add('show');
        document.body.style.overflow = 'hidden';

        // Update navigation buttons
        this.updateNavigationButtons();
    }
    
    closeModal() {
        this.modal.classList.remove('show');
        document.body.style.overflow = '';
    }
    
    previousImage() {
        if (this.currentImages.length === 0) return;
        
        this.currentIndex = (this.currentIndex - 1 + this.currentImages.length) % this.currentImages.length;
        this.updateModalImage();
    }
    
    nextImage() {
        if (this.currentImages.length === 0) return;
        
        this.currentIndex = (this.currentIndex + 1) % this.currentImages.length;
        this.updateModalImage();
    }
    
    updateModalImage() {
        const img = this.currentImages[this.currentIndex];
        if (img) {
            this.modalImage.src = img.src;
            this.modalImage.alt = img.alt;
            this.modalCaption.textContent = img.dataset.caption;
            this.modalCategory.textContent = img.dataset.category;

            // Handle commentary
            const commentary = img.dataset.commentary;
            if (commentary && commentary.trim()) {
                this.modalCommentary.textContent = commentary;
                this.modalCommentary.style.display = 'block';
            } else {
                this.modalCommentary.style.display = 'none';
            }

            this.updateNavigationButtons();
        }
    }
    
    updateNavigationButtons() {
        // Hide navigation buttons if there's only one image or no images
        if (this.currentImages.length <= 1) {
            this.modalPrev.style.display = 'none';
            this.modalNext.style.display = 'none';
        } else {
            this.modalPrev.style.display = 'block';
            this.modalNext.style.display = 'block';
        }
    }
}

// Initialize gallery when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if we're on the gallery page
    if (document.getElementById('gallery-grid')) {
        new Gallery();
    }
});

// Add lazy loading for images and error handling
document.addEventListener('DOMContentLoaded', () => {
    const images = document.querySelectorAll('img[loading="lazy"]');

    // Add error handling for all gallery images
    images.forEach(img => {
        img.addEventListener('error', function() {
            console.warn('Failed to load image:', this.src);
            this.style.display = 'none';
            // Hide the parent gallery item if image fails to load
            const galleryItem = this.closest('.gallery-item');
            if (galleryItem) {
                galleryItem.style.display = 'none';
            }
        });

        img.addEventListener('load', function() {
            console.log('Successfully loaded image:', this.src);
        });
    });

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.src; // Trigger loading
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }
});

// Add masonry layout adjustment on window resize
window.addEventListener('resize', () => {
    // Debounce resize events
    clearTimeout(window.resizeTimeout);
    window.resizeTimeout = setTimeout(() => {
        // Re-trigger any layout calculations if needed
        const galleryGrid = document.getElementById('gallery-grid');
        if (galleryGrid) {
            // Force reflow to adjust masonry layout
            galleryGrid.style.display = 'none';
            galleryGrid.offsetHeight; // Trigger reflow
            galleryGrid.style.display = 'grid';
        }
    }, 250);
});
