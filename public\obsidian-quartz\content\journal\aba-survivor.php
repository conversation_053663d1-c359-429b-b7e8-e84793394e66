<?php
// Auto-generated blog post
// Source: aba-survivor.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Explaining ABA Therapy and What It Means to Be a Survivor';
$meta_description = 'This approach often doesn\'t genuinely help the child. Instead, it can offer short-term reassurance to parents who desire a "normal" child..';
$meta_keywords = 'journal, advocacy, homeless, aachips, survivor, autism, a-b-a, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../img/self/walkinghere.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Explaining ABA Therapy and What It Means to Be a Survivor',
  'date' => '2025-05-01',
  'author' => 'A. A. Chips',
  'categories' => 
  array (
    0 => 'Journal',
  ),
  'tags' => 
  array (
    0 => 'journal',
    1 => 'advocacy',
    2 => 'homeless',
    3 => 'aachips',
    4 => 'survivor',
    5 => 'autism',
    6 => 'a-b-a',
  ),
  'excerpt' => 'This approach often doesn\'t genuinely help the child. Instead, it can offer short-term reassurance to parents who desire a "normal" child..',
  'thumbnail' => '../../img/self/walkinghere.jpg',
  'source_file' => 'content\\journal\\aba-survivor.md',
);

// Raw content
$post_content = '<p>I am a survivor of ABA therapy. ABA therapy can be compared to conversion therapy, but for autistic children. It\'s a broad term for therapies that aim to make an autistic child appear "normal." Instead of helping the child cope with challenges like sensory sensitivities, communication difficulties, or understanding food aversions, it often focuses on teaching social behaviors such as table manners, suppressing self-regulatory actions (like hand flapping), and forcing eye contact.</p>

<p>This approach often doesn\'t genuinely help the child. Instead, it can offer short-term reassurance to parents who desire a "normal" child, which is deeply problematic. While my parents did the best they could with the information they had, this therapy can stifle creativity and joy. During my formative years, it damaged my ability to trust others. For a period, it led me to act out, causing pain to those closest to me. I exhibited bigoted and bullying behaviors and turned to drugs for self-soothing for many years. I also experienced mental distress for a time, though I have since found peace.</p>

<p>Today, I live a sober life. I am emotionally regulated and confronting life challenges that would have been easier to address earlier. Tens of thousands of dollars were spent on harmful treatments that have resulted in long-term issues rather than effective coping skills. I still struggle to assert my needs and boundaries and have them fully respected.</p>

<p>Despite these significant concerns, ABA is still widely regarded as a valid, safe, and helpful therapy. There are residential schools that specialize in it, many of which have faced numerous abuse allegations. The ideology also contains subtle and overt biases. It\'s common for children as young as six to be enrolled in 40 hours a week of ABA therapy services. Disabled children deserve better.</p>

<p>I won\'t elaborate on my personal experiences here, but I am open to discussing them on other platforms if it helps others. I can answer questions here. Please understand that if you attempt to debate the validity of my experiences, my responses may be direct.</p>

<p>Additionally, when I lack a verbal outlet, it\'s easy for me to internalize problems. This leads to stress, which can cause chronic pain flares and disruptions to my body.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>