<?php
// Auto-generated category index
// Category: alienation

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Family Alienation and Recovery Content';
$meta_description = 'Family alienation and recovery content.';
$meta_keywords = 'alienation, posts, A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$related_posts = [];

// Category posts data
$category_posts = array (
  0 => 
  array (
    'title' => 'Connecting on Discord and Safety Tips for Minors',
    'author' => NULL,
    'date' => '2025-05-20',
    'excerpt' => 'Guidelines and safety practices for young people using Discord and other online platforms',
    'url' => 'discord-minors-safety.php',
    'tags' => 
    array (
      0 => 'online-safety',
      1 => 'discord',
      2 => 'youth',
      3 => 'resources',
      4 => 'draft',
    ),
    'filename' => 'discord-minors-safety',
    'thumbnail' => 'https://tse2.mm.bing.net/th/id/OIP.HDeK6xsVfNg8x0z7rVWIKQHaHa?rs=1&pid=ImgDetMain',
  ),
  1 => 
  array (
    'title' => 'Alienation is a Crime Against your Children',
    'author' => 'A. A. Chips',
    'date' => '2025-05-11',
    'excerpt' => 'Here are two recordings from separate court proceedings involving Parental Alienation being charged and convicted. These are rare and few between, gen...',
    'url' => 'alienation-crime.php',
    'tags' => 
    array (
      0 => 'alienation',
      1 => 'advocacy',
      2 => 'teens',
      3 => 'recovery',
    ),
    'filename' => 'alienation-crime',
    'thumbnail' => 'https://static.standard.co.uk/s3fs-public/thumbnails/image/2018/01/25/12/p6.jpg?width=968&auto=webp&quality=75&crop=968:645%2Csmart',
  ),
  2 => 
  array (
    'title' => 'Finding Their Way Back: Reunification After Alienation',
    'author' => 'Mandy Louise Matthewson, Jessica Bowring, Jacinta Hickey, Sophie Ward, Peta Diercke, Leesa Van Niekerk',
    'date' => '2025-05-11',
    'excerpt' => 'Parental alienation is a painful reality for many families going through separation or divorce. It happens when one parent (the alienating parent) turns a child against the other loving parent (the targeted parent). This can lead to a complete breakdown in the relationship, leaving both the targeted parent and the child heartbroken.',
    'url' => 'finding-way-back.php',
    'tags' => 
    array (
      0 => 'alienation',
      1 => 'advocacy',
      2 => 'library',
    ),
    'filename' => 'finding-way-back',
    'thumbnail' => '../../img/self/DirtRoad.jpg',
  ),
  3 => 
  array (
    'title' => 'The Aftermath of Ending Contact',
    'author' => 'A. A. Chips',
    'date' => '2025-03-03',
    'excerpt' => 'Deciding to step back from family relationships—even when necessary—rarely brings the clean closure we hope for. What follows is often a quiet storm of emotions: grief, anger, guilt, and even unexpected loneliness. If you’ve made this choice, you might be grappling with feelings that seem contradictory or overwhelming.',
    'url' => 'aftermath-ending-contact.php',
    'tags' => 
    array (
      0 => 'alienation',
      1 => 'advocacy',
      2 => 'addiction',
    ),
    'filename' => 'aftermath-ending-contact',
    'thumbnail' => 'https://exbackpermanently.com/wp-content/uploads/2013/10/no-contact.jpg',
  ),
  4 => 
  array (
    'title' => '\'I Was Safer in a Graveyard than at Home’ -- Tehmina\'s Story',
    'author' => 'The Anti-Alienation Project',
    'date' => '2024-10-07',
    'excerpt' => 'Join me as I delve into Tehmina\'s harrowing--yet empowering--story of overcoming parental alienation and family betrayal. Tehmina, known on TikTok as \'Alienated Child Speaks\', shares: “I found my voice through my pain..”',
    'url' => 'safer-in-graveyard.php',
    'tags' => 
    array (
      0 => 'alienation',
    ),
    'filename' => 'safer-in-graveyard',
    'thumbnail' => 'https://media.cntraveler.com/photos/5bfda3d745e16c465f8757be/16:9/w_2560%2Cc_limit/Assistens-Cemetery_P5154875.jpg',
  ),
  5 => 
  array (
    'title' => 'Must-Watch for Anyone with Divorced Parents',
    'author' => NULL,
    'date' => '2024-09-28',
    'excerpt' => 'It can take years to recognize that a parent-child bond has been fractured by manipulation and lies. This is the reality for many who have experienced parental alienation, a severe form of psychological child abuse. Often stemming from conflict between parents, be it fighting, separation, or divorce, parental alienation involves one parent turning a child against the other without true justification.',
    'url' => 'must-watch-divorcing-parents.php',
    'tags' => 
    array (
      0 => 'alienation',
      1 => 'advocacy',
      2 => 'teens',
      3 => 'video',
    ),
    'filename' => 'must-watch-divorcing-parents',
    'thumbnail' => 'https://www.childcentereddivorce.com/wp-content/uploads/2014/07/Parents-w-kid-between.jpg',
  ),
  6 => 
  array (
    'title' => 'I\'m 61 Years Old, and It Still Affects Me Today’  Dan’s Alienation Horror Story',
    'author' => 'The Anti-Alienation Project',
    'date' => '2024-08-01',
    'excerpt' => 'Meet Dan, a remarkable survivor of parental alienation, having experienced this psychological abuse both as a child and a parent. In this interview, Dan shares the emotional turmoil he faced growing up, from being alienated from his father from the tender age of just nine months to watching his little sister undergo the same alienation tactics that he did as a child.',
    'url' => 'dans-story.php',
    'tags' => 
    array (
      0 => 'alienation',
    ),
    'filename' => 'dans-story',
    'thumbnail' => 'https://i.ytimg.com/vi/IKJzMZOzL8M/maxresdefault.jpg',
  ),
  7 => 
  array (
    'title' => '16 Things I Would Tell Myself (While Alienated)',
    'author' => 'The Anti-Alienation Project',
    'date' => '2024-07-25',
    'excerpt' => 'Dealing with alienation, especially from family, is an incredibly painful experience. The emotional turmoil and confusion can feel overwhelming. Inspired by the insights of the Anti-Alienation Project, here are 16 truths to hold onto as you navigate this challenging journey.',
    'url' => '16-truths-alienation.php',
    'tags' => 
    array (
      0 => 'alienation',
    ),
    'filename' => '16-truths-alienation',
    'thumbnail' => 'https://www.deltacommunitychurch.co.za/wp-content/uploads/2020/07/Reaching-out-a-helping-hand-.jpg',
  ),
  8 => 
  array (
    'title' => 'The Child Who Is Not Embraced By The Village Will Burn It Down To Feel Its Warmth',
    'author' => 'Captain Axom - Medium.com',
    'date' => '2023-11-23',
    'excerpt' => 'Exploring the proverb "The child who is not embraced by the village will burn it down to feel its warmth" and its implications for homelessness and community building.',
    'url' => 'child-not-embraced.php',
    'tags' => 
    array (
      0 => 'homelessness',
      1 => 'advocacy',
      2 => 'alienation',
    ),
    'filename' => 'child-not-embraced',
    'thumbnail' => 'https://miro.medium.com/v2/resize:fit:779/1*Y54NI2ibacMGs9-rsEwciw.jpeg',
  ),
  9 => 
  array (
    'title' => 'A Friendly Introduction into Supported Decision Making',
    'author' => NULL,
    'date' => '2022-06-12',
    'excerpt' => 'The traditional model of decision-making for individuals with disabilities often positions them as incapable and needing others to decide for them. This outdated approach, rooted in a guardianship or conservatorship model, has perpetuated harmful stereotypes and denied individuals their fundamental right to autonomy.',
    'url' => 'supported-decision-making.php',
    'tags' => 
    array (
      0 => 'hhs',
      1 => 'april',
      2 => 'writings',
      3 => 'resources',
      4 => 'library',
      5 => 'disability',
      6 => 'accessibility',
      7 => 'sdm',
    ),
    'filename' => 'supported-decision-making',
    'thumbnail' => 'https://www.laurabaker.org/wp-content/uploads/FNS-Blog-Supported-Decision-Making-2.jpg',
  ),
  10 => 
  array (
    'title' => '200 Blunt Words from Minnesota Judge for Divorcing Parents',
    'author' => 'Judge Michael Haas',
    'date' => '2001-01-01',
    'excerpt' => 'No matter what you think of the other party—or what your family thinks of the other party—these children are one-half of each of your. Remember that, because every time you tell your child what an “idiot” his father is, or what a “fool” his mother is, or how bad the absent parent is, or what terrible things that person has done, you are telling the child half of him is bad.',
    'url' => '200-blunt-words.php',
    'tags' => 
    array (
      0 => 'alienation',
      1 => 'advocacy',
    ),
    'filename' => '200-blunt-words',
    'thumbnail' => '../../img/edutainment/200BluntWords.jpg',
  ),
  11 => 
  array (
    'title' => 'Alternatives to \'I love you\'',
    'author' => 'Sue Ellson',
    'date' => NULL,
    'excerpt' => 'How to show you care without saying \'I love you\'',
    'url' => 'alternatives-to-iloveyou.php',
    'tags' => 
    array (
      0 => 'memes',
      1 => 'alienation',
    ),
    'filename' => 'alternatives-to-iloveyou',
    'thumbnail' => 'https://winkgo.com/wp-content/uploads/2019/03/101-i-love-you-memes-17-768x576.jpg',
  ),
  12 => 
  array (
    'title' => 'Bill of Rights for Teen for Healthy Family Relationships',
    'author' => 'Erasing Families',
    'date' => NULL,
    'excerpt' => 'Every teenager deserves to feel safe, respected, and supported within their family. This Bill of Rights outlines the important rights you have as a fifteen-year-old to foster a healthy and positive home environment.',
    'url' => 'teen-bill-rights.php',
    'tags' => 
    array (
      0 => 'alienation',
      1 => 'advocacy',
      2 => 'lgbt',
    ),
    'filename' => 'teen-bill-rights',
    'thumbnail' => 'https://media.licdn.com/dms/image/D4E12AQEs3_7aL_bgtw/article-cover_image-shrink_720_1280/0/1693497305984?e=**********&v=beta&t=rYHSWfR-E4bZ2c7VNT-QUDUNjgL0XBjjekF6cR4xvug',
  ),
  13 => 
  array (
    'title' => 'Skit talking bad about the other parent',
    'author' => 'Steph the Attachment Therapist',
    'date' => NULL,
    'excerpt' => 'This skit by Steph the Attachment Therapist goes into what happens when a parent badmouths the other parent in front of the child.',
    'url' => 'attachment-therapist.php',
    'tags' => 
    array (
      0 => 'alienation',
    ),
    'filename' => 'attachment-therapist',
    'thumbnail' => 'https://bloximages.newyork1.vip.townnews.com/wenatcheeworld.com/content/tncms/assets/v3/editorial/3/84/384d538a-4040-11ed-b923-5b40f282fbd3/633612728c931.image.jpg?crop=1600%2C840%2C0%2C113&resize=1200%2C630&order=crop%2Cresize',
  ),
  14 => 
  array (
    'title' => 'The First Time I Realized My Dad Wasn\'t Who They Said He Was',
    'author' => 'Ryan Thomas',
    'date' => NULL,
    'excerpt' => 'There was a turning point, a crystalizing moment when everything I thought I knew about my dad fractured. For years, I’d been taught a narrative, a carefully constructed image of him that simply wasn\'t real.',
    'url' => 'dad-wasnt-who-they-said.php',
    'tags' => 
    array (
      0 => 'alienation',
    ),
    'filename' => 'dad-wasnt-who-they-said',
    'thumbnail' => 'https://i.imgflip.com/3bl2r5.jpg',
  ),
  15 => 
  array (
    'title' => 'Tips for Schools to Support Families Experiencing Custody Issues',
    'author' => 'Wendy Perry Co-Parenting & Alienation Support',
    'date' => NULL,
    'excerpt' => '\\"Make co-parenting the school\'s policy, not the child\'s burden.\\"',
    'url' => 'tips-for-schools.php',
    'tags' => 
    array (
      0 => 'alienation',
      1 => 'advocacy',
    ),
    'filename' => 'tips-for-schools',
    'thumbnail' => 'https://media.licdn.com/dms/image/D5612AQGd_iF2oBQzsA/article-cover_image-shrink_720_1280/0/1680973280752?e=**********&v=beta&t=DqjrGtyYCmPSxOklUSFlJowT3GpKXSkCpFO-EHNQ_Zs',
  ),
  16 => 
  array (
    'title' => 'Trauma Survivor\'s Manifesto',
    'author' => 'Unknown',
    'date' => NULL,
    'excerpt' => 'What happened to me is real. It\'s impact on me is profound. I have a right to be heard. I have a right to be taken seriously. I have a right to grieve for as long as it takes.',
    'url' => 'survivor-manifesto.php',
    'tags' => 
    array (
      0 => 'alienation',
      1 => 'ptsd',
    ),
    'filename' => 'survivor-manifesto',
    'thumbnail' => '../../img/edutainment/traumamanifesto.jpg',
  ),
  17 => 
  array (
    'title' => 'What Could Have Saved You as an Alienated Kid?',
    'author' => 'The Anti-Alienation Project',
    'date' => NULL,
    'excerpt' => 'Drawing a powerful metaphor from her three-year-old\'s traumatic tooth extraction – a necessary short-term pain to prevent long-term damage – Maddie outlines what she believes could have "pulled out the rotten tooth" of her alienation.',
    'url' => 'what-could-have.php',
    'tags' => 
    array (
      0 => 'alienation',
      1 => 'teens',
    ),
    'filename' => 'what-could-have',
    'thumbnail' => 'https://www.wealthify.com/media/1652/save-for-your-child-800.jpg',
  ),
);

// Generate content
ob_start();
?>
<div class="category-index">
    <header class="category-header">
        <h1><?php echo htmlspecialchars($page_title); ?></h1>
    </header>

    <div class="category-content">
        <?php echo <<<'HTML'
<p>I'm an alienated family member whose actually wonderful.</p>

<p>If you need someone supportive in your life. Who believes in you, and will deliver helpful content. Look no further and subscribe. And maybe comment some questions, video ideas, or a bit about yourself.</p>

<p>I care about your feedback and ideas. Let me know what you would like to see.</p>

<p>if you've heard rumors about me, they are probably true.  Even if it's not.</p>

<p>Help out the algorithm by hitting the right buttons. I got to find someone on here.</p>

<p>I hope you have a great day. If you haven't, go and drink some water.</p>

<p>I put together this vault of information about Parental and Family Alienation. As helpful as it can be, it is also distressing information to swallow.</p>

<p>I'm putting this page together to start a conversation on a topic that is very difficult. I consider this to be a generational curse in my family of upbringing. When I talk about Alienation, it can mean a lot of different things to different people.</p>

<img src="../../img/art/painFamilies.jpg" alt="Pain travels through families until somebody is ready to feel it. Quote by Stephi Wagner." width="400">

<p>To me, what Alienation is is the constant need for there to be an enemy, or someone to throw under the bus. I witnessed this behavior my entire life and didn't speak on it, until I was on the receiving end. This happens through manipulation, conveying of exaggerated or false information, and attacks on one's character. It is generally done out of extreme fear and insecurity, but can be done maliciously. Kids get put in the middle of grown up conflicts, and everyone ends up losing.</p>

<p>It is a form of psychological abuse and violence. There is settled research on this phenomenon, and lots of pseudoscience and crazy people who speak on it as well. There are people who dispute the existence of Parental and Family Alienation, and generally these are not good faith arguments, but ones of financial conflicts of interest. This comes up during high conflict divorces, separations, and estrangements. It is not relevant to everyone's situation. But it's absolutely relevant to mine.</p>

<p>I am not putting this together to argue a case, or argue that I am a good person, or attack anyone. If the shoe fits, it fits, however.</p>

<p>I'm putting this together because for the past eight years, I haven't really been allowed to share my point of view around the people who are doing this. It has impacted me heavily and inflicted a heartache and dread I wouldn't wish on my worst enemy, let alone the kids who are burdened to carry the weight of this their entire life.</p>

<p>I'm sharing this because the young people caught as child soldiers in a divorce and estrangement war deserve to hear the other side, even if it is painful. It is also freeing and liberating. I love you and I'm sorry you were put in the middle of this.</p>



HTML;
        ?>
    </div>

    <div class="post-grid">
        <?php foreach ($category_posts as $post): ?>
            <div class="post-card">
                <a href="<?php echo htmlspecialchars($post['url']); ?>" class="post-card-link">
                <div class="post-card-thumbnail">
                    <?php if (isset($post['thumbnail']) && $post['thumbnail']): ?>
                        <?php if (preg_match('/^https?:\/\//', $post['thumbnail'])): ?>
                            <img src="<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php else: ?>
                            <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php endif; ?>
                    <?php else: ?>
                        <?php $placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>
                        <?php $random_placeholder = $placeholder_images[array_rand($placeholder_images)]; ?>
                        <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo $random_placeholder; ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img placeholder">
                    <?php endif; ?>
                </div>
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars(is_string($post['excerpt']) ? $post['excerpt'] : ''); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post['author']) && $post['author'] && is_string($post['author'])): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post['date']) && $post['date']): ?>
                            <span class="post-date"><?php echo is_string($post['date']) ? htmlspecialchars($post['date']) : ''; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>