{"Self Album": [{"filename": "self/headshot.jpg", "alt": "Professional headshot of A. A. Chips", "caption": "Professional headshot", "width": 800, "height": 600, "commentary": "This was taken during a time when I was trying to rebuild my professional image. After experiencing homelessness and family alienation, having a decent headshot felt like reclaiming a piece of my identity and dignity."}, {"filename": "self/selfie1.jpg", "alt": "Casual selfie of A. A. Chips", "caption": "Casual selfie", "width": 600, "height": 800, "commentary": ""}, {"filename": "self/selfie3.jpg", "alt": "Another selfie of A. A. Chips", "caption": "Another selfie", "width": 600, "height": 800}, {"filename": "self/evgselfie.jpg", "alt": "Evening selfie", "caption": "Evening selfie", "width": 600, "height": 800}, {"filename": "self/mowie-and-dad.jpg", "alt": "<PERSON><PERSON> <PERSON><PERSON> with <PERSON><PERSON><PERSON>", "caption": "<PERSON> and <PERSON><PERSON><PERSON>", "width": 800, "height": 600, "commentary": "<PERSON><PERSON><PERSON> has been my constant companion through the hardest times. This little dog has seen me at my lowest and highest points. When I had nothing else, I had <PERSON><PERSON><PERSON>'s unconditional love and loyalty. She's not just a pet - she's family, and often the only family that felt safe and reliable."}, {"filename": "self/mowie-hammock.jpg", "alt": "<PERSON><PERSON><PERSON> in a hammock", "caption": "<PERSON><PERSON><PERSON> relaxing in the hammock", "width": 800, "height": 600}, {"filename": "mowie-quilt.jpg", "alt": "<PERSON><PERSON><PERSON> on a quilt", "caption": "<PERSON><PERSON><PERSON> on the quilt", "width": 800, "height": 600}, {"filename": "mowielake.jpg", "alt": "Mowie by the lake", "caption": "<PERSON><PERSON><PERSON> enjoying the lake", "width": 800, "height": 600}, {"filename": "car-bed.jpg", "alt": "Sleeping setup in car", "caption": "My car bed setup", "width": 800, "height": 600, "commentary": "This represents both resourcefulness and necessity. When you don't have a home, you make one wherever you can. I spent months perfecting this setup - making it as comfortable and dignified as possible. It's a reminder that home isn't always a building; sometimes it's just a safe space you create for yourself."}, {"filename": "car-bookshelf.jpg", "alt": "Bookshelf in car", "caption": "My mobile library", "width": 800, "height": 600}, {"filename": "messycar.jpg", "alt": "Interior of lived-in car", "caption": "Real life in the car", "width": 800, "height": 600}, {"filename": "comfy1.jpg", "alt": "Comfortable living space", "caption": "Making it comfortable", "width": 800, "height": 600}, {"filename": "comfy2.jpg", "alt": "Another comfortable setup", "caption": "Cozy corner", "width": 800, "height": 600}], "Funny Memes": [{"filename": "clickhole.jpg", "alt": "Clickhole meme", "caption": "Classic Clickhole humor", "width": 600, "height": 400}, {"filename": "douchebumps.png", "alt": "Douchebumps meme", "caption": "Douchebumps - when someone's being awful", "width": 500, "height": 500, "commentary": "This perfectly captures that physical reaction you get when someone is being particularly awful or manipulative. I've experienced this so many times - that visceral, skin-crawling feeling when someone's behavior is just fundamentally wrong. It's like your body knows before your mind fully processes it."}, {"filename": "lurkLaughLoathe.png", "alt": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>athe meme", "caption": "The three stages of internet browsing", "width": 600, "height": 400}, {"filename": "momImFat.jpg", "alt": "Mom I'm fat meme", "caption": "Relatable food struggles", "width": 500, "height": 600}, {"filename": "moms-way-worse.jpg", "alt": "Mom's way worse meme", "caption": "When mom one-ups your problems", "width": 600, "height": 400}, {"filename": "new-phone.jpg", "alt": "New phone meme", "caption": "The eternal new phone struggle", "width": 500, "height": 600}, {"filename": "offensive.jpg", "alt": "Offensive humor meme", "caption": "When humor goes too far", "width": 600, "height": 400}, {"filename": "peoplepleasing.jpg", "alt": "People pleasing meme", "caption": "The people pleaser's dilemma", "width": 600, "height": 400}, {"filename": "squick.png", "alt": "Squick reaction meme", "caption": "That uncomfortable feeling", "width": 400, "height": 400}, {"filename": "toot.jpg", "alt": "Toot meme", "caption": "Simple pleasures", "width": 500, "height": 500}, {"filename": "trolls.jpg", "alt": "Internet trolls meme", "caption": "Dealing with internet trolls", "width": 600, "height": 400}, {"filename": "vermin.jpg", "alt": "Vermin meme", "caption": "When you're called vermin", "width": 600, "height": 400}, {"filename": "villain.jpg", "alt": "Villain meme", "caption": "Are we the baddies?", "width": 600, "height": 400}, {"filename": "were-all-the-villain.png", "alt": "We're all the villain meme", "caption": "Plot twist: we're all the villain", "width": 600, "height": 400}, {"filename": "whatever.png", "alt": "Whatever meme", "caption": "The ultimate dismissal", "width": 500, "height": 500}, {"filename": "why-i-dont-want-kids.png", "alt": "Why I don't want kids meme", "caption": "Honest reasons about not wanting kids", "width": 600, "height": 800}], "Edutainment": [{"filename": "<PERSON>-Ganz-storytelling.png", "alt": "<PERSON> storytelling diagram", "caption": "The power of narrative in organizing", "width": 800, "height": 600}, {"filename": "CriticalRaceTheory.jpg", "alt": "Critical Race Theory explanation", "caption": "Understanding Critical Race Theory", "width": 800, "height": 600}, {"filename": "consentcommunication.jpg", "alt": "Consent communication guide", "caption": "Healthy communication about consent", "width": 600, "height": 800}, {"filename": "seedsOfCharacter.jpg", "alt": "Seeds of character quote", "caption": "Character development wisdom", "width": 600, "height": 400}, {"filename": "weDontShapeChildren.jpg", "alt": "Quote about shaping children", "caption": "Children shape themselves", "width": 600, "height": 400}, {"filename": "childNotEmbraced.webp", "alt": "Child not embraced by village quote", "caption": "The importance of community support", "width": 600, "height": 400}, {"filename": "traumamanifesto.jpg", "alt": "Trauma manifesto", "caption": "Understanding trauma responses", "width": 600, "height": 800, "commentary": "This image speaks to the heart of my advocacy work. Too often, trauma responses are pathologized or dismissed instead of understood as normal reactions to abnormal situations. When you've been through family alienation, homelessness, and systemic failures, your responses make perfect sense - they're survival mechanisms, not character flaws."}, {"filename": "inflammationRelief.jpg", "alt": "Inflammation relief guide", "caption": "Natural inflammation relief methods", "width": 600, "height": 800}, {"filename": "unlearnyourpain.jpg", "alt": "Unlearn your pain book cover", "caption": "Chronic pain management approach", "width": 400, "height": 600}, {"filename": "LEARNING.jpg", "alt": "Learning motivation poster", "caption": "The importance of continuous learning", "width": 600, "height": 400}, {"filename": "schoolIntegration.jpg", "alt": "School integration historical image", "caption": "Historical perspective on school integration", "width": 800, "height": 600}, {"filename": "poor-peoples-campaign-principles.jpg", "alt": "Poor People's Campaign principles", "caption": "Social justice organizing principles", "width": 600, "height": 800}], "Art": [{"filename": "rosegrandpasvg.svg", "alt": "A. A. Chips logo - rose with grandpa figure", "caption": "Site logo design", "width": 100, "height": 100, "commentary": "This logo represents the intersection of beauty and wisdom, growth and experience. The rose symbolizes resilience - something that can bloom even in difficult conditions. The grandpa figure represents the wisdom that comes from lived experience, especially the hard-won wisdom of surviving trauma and rebuilding."}, {"filename": "aachipslogoupdate.png", "alt": "Updated A. A. Chips logo", "caption": "Logo update design", "width": 400, "height": 400}, {"filename": "possum-painting-29.jpg", "alt": "Possum painting artwork", "caption": "Possum art piece #29", "width": 600, "height": 800}, {"filename": "possum.png", "alt": "Possum illustration", "caption": "Cute possum illustration", "width": 400, "height": 400}, {"filename": "possumfriends.png", "alt": "Possum friends illustration", "caption": "Possum friendship art", "width": 600, "height": 400}, {"filename": "ink_drawing_of_an_ear,_fine_dot_shading,_retro_flat_colors_aiseo_art.jpg", "alt": "Artistic ink drawing of an ear", "caption": "Detailed ear illustration with dot shading", "width": 600, "height": 600}, {"filename": "honeycomb.png", "alt": "Honeycomb pattern art", "caption": "Geometric honeycomb design", "width": 500, "height": 500}, {"filename": "cactus.png", "alt": "Cactus illustration", "caption": "Desert cactus art", "width": 400, "height": 600}, {"filename": "wheel.webp", "alt": "Artistic wheel design", "caption": "Decorative wheel artwork", "width": 500, "height": 500}, {"filename": "tapestry.jpg", "alt": "Tapestry artwork", "caption": "Beautiful tapestry design", "width": 800, "height": 600}, {"filename": "fotor-ai-2024060520457.jpg", "alt": "AI-generated artwork 1", "caption": "AI-assisted creative piece", "width": 600, "height": 600}, {"filename": "fotor-ai-2024060520539.jpg", "alt": "AI-generated artwork 2", "caption": "Another AI-assisted creation", "width": 600, "height": 600}]}