<?php
/**
 * Content Validation Script
 * Validates YAML frontmatter and content structure
 */

if (php_sapi_name() !== 'cli') {
    die("This script must be run from the command line.\n");
}

class ContentValidator {
    private $contentDir = 'content';
    private $errors = [];
    private $warnings = [];

    public function validate() {
        echo "Validating content files...\n\n";
        
        $this->validateDirectory($this->contentDir);
        
        $this->printResults();
    }

    private function validateDirectory($dir) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir)
        );

        foreach ($iterator as $file) {
            if ($file->getExtension() === 'md') {
                $this->validateFile($file);
            }
        }
    }

    private function validateFile($file) {
        $filepath = $file->getPathname();
        $content = file_get_contents($filepath);
        
        echo "Validating: " . str_replace($this->contentDir . DIRECTORY_SEPARATOR, '', $filepath) . "\n";

        // Check for frontmatter
        if (!preg_match('/^---\s*\n(.*?)\n---\s*\n(.*)$/s', $content, $matches)) {
            $this->errors[] = "$filepath: No valid YAML frontmatter found";
            return;
        }

        $yamlContent = $matches[1];
        $markdownContent = $matches[2];

        // Parse YAML frontmatter
        $frontmatter = $this->parseYaml($yamlContent);
        
        // Validate required fields
        $this->validateRequiredFields($filepath, $frontmatter);
        
        // Validate field formats
        $this->validateFieldFormats($filepath, $frontmatter);
        
        // Validate content
        $this->validateContent($filepath, $markdownContent);
    }

    private function parseYaml($yamlContent) {
        $frontmatter = [];
        $lines = explode("\n", $yamlContent);
        $currentKey = null;

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            // Handle array items
            if (strpos($line, '-') === 0) {
                if ($currentKey && !isset($frontmatter[$currentKey])) {
                    $frontmatter[$currentKey] = [];
                }
                if ($currentKey) {
                    $frontmatter[$currentKey][] = trim(substr($line, 1));
                }
            } elseif (strpos($line, ':') !== false) {
                [$key, $value] = explode(':', $line, 2);
                $key = trim($key);
                $value = trim($value);
                $currentKey = $key;

                if (!empty($value)) {
                    $frontmatter[$key] = $value;
                }
            }
        }

        return $frontmatter;
    }

    private function validateRequiredFields($filepath, $frontmatter) {
        $required = ['title', 'date', 'author'];
        
        foreach ($required as $field) {
            if (!isset($frontmatter[$field]) || empty($frontmatter[$field])) {
                $this->errors[] = "$filepath: Missing required field '$field'";
            }
        }
    }

    private function validateFieldFormats($filepath, $frontmatter) {
        // Validate date format
        if (isset($frontmatter['date'])) {
            if (strtotime($frontmatter['date']) === false) {
                $this->warnings[] = "$filepath: Date format may be invalid: {$frontmatter['date']}";
            }
        }

        // Validate tags
        if (isset($frontmatter['tags'])) {
            if (!is_array($frontmatter['tags']) && !is_string($frontmatter['tags'])) {
                $this->warnings[] = "$filepath: Tags should be an array or string";
            }
        }

        // Check for excerpt
        if (!isset($frontmatter['excerpt'])) {
            $this->warnings[] = "$filepath: Consider adding an excerpt for better SEO";
        }
    }

    private function validateContent($filepath, $content) {
        // Check for empty content
        if (trim($content) === '') {
            $this->warnings[] = "$filepath: Content is empty";
            return;
        }

        // Check for basic markdown structure
        if (!preg_match('/^#\s+/', $content)) {
            $this->warnings[] = "$filepath: Consider starting with a main heading (# Title)";
        }

        // Check for very short content
        if (str_word_count($content) < 10) {
            $this->warnings[] = "$filepath: Content seems very short";
        }
    }

    private function printResults() {
        echo "\n" . str_repeat("=", 50) . "\n";
        echo "VALIDATION RESULTS\n";
        echo str_repeat("=", 50) . "\n\n";

        if (empty($this->errors) && empty($this->warnings)) {
            echo "✅ All content files are valid!\n";
            return;
        }

        if (!empty($this->errors)) {
            echo "❌ ERRORS (" . count($this->errors) . "):\n";
            foreach ($this->errors as $error) {
                echo "  - $error\n";
            }
            echo "\n";
        }

        if (!empty($this->warnings)) {
            echo "⚠️  WARNINGS (" . count($this->warnings) . "):\n";
            foreach ($this->warnings as $warning) {
                echo "  - $warning\n";
            }
            echo "\n";
        }

        echo "Total files with issues: " . count(array_unique(array_merge($this->errors, $this->warnings))) . "\n";
    }
}

// Run validation
$validator = new ContentValidator();
$validator->validate();
?>
