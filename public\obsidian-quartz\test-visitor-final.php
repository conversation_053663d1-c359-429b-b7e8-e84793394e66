<?php
/**
 * Final Visitor Counter Test
 * Tests the working visitor counter system
 */

echo "<h1>🎯 Visitor Counter Final Test</h1>\n";
echo "<p>Testing the working visitor counter system...</p>\n";

try {
    // Test database connection
    echo "<h2>Step 1: Database Connection</h2>\n";
    require_once 'comments/database.php';
    $db = CommentDatabase::getInstance();
    echo "✅ Database connection successful<br>\n";
    
    // Test visitor counter class
    echo "<h2>Step 2: Visitor Counter Class</h2>\n";
    require_once 'visitor-counter/VisitorCounter.php';
    $counter = new VisitorCounter($db);
    echo "✅ Visitor counter class loaded<br>\n";
    
    // Test recording a visit
    echo "<h2>Step 3: Recording a Visit</h2>\n";
    $testSlug = 'final-test-' . date('H-i-s');
    $result = $counter->recordVisit($testSlug, 'Final Test Page');
    
    if ($result) {
        echo "✅ Visit recorded successfully<br>\n";
    } else {
        echo "ℹ️ Visit not recorded (might be duplicate)<br>\n";
    }
    
    // Test getting page stats
    echo "<h2>Step 4: Getting Page Statistics</h2>\n";
    $pageStats = $counter->getPageStats($testSlug);
    if ($pageStats) {
        echo "✅ Page statistics retrieved<br>\n";
        echo "📊 Total visits: {$pageStats['total_visits']}<br>\n";
        echo "👥 Unique visits: {$pageStats['unique_visits']}<br>\n";
        echo "📅 Today's visits: {$pageStats['today_visits']}<br>\n";
    } else {
        echo "❌ Failed to get page statistics<br>\n";
    }
    
    // Test getting site stats
    echo "<h2>Step 5: Getting Site Statistics</h2>\n";
    $siteStats = $counter->getSiteStats();
    if (!empty($siteStats)) {
        echo "✅ Site statistics retrieved<br>\n";
        echo "🌐 Total site visits: " . ($siteStats['total_site_visits'] ?? 0) . "<br>\n";
        echo "👥 Unique site visitors: " . ($siteStats['unique_site_visitors'] ?? 0) . "<br>\n";
    } else {
        echo "❌ Failed to get site statistics<br>\n";
    }
    
    // Test display functions
    echo "<h2>Step 6: Testing Display Functions</h2>\n";
    require_once 'visitor-counter/visitor-display.php';
    
    echo "<h3>Default Display:</h3>\n";
    echo displayVisitorCounter($testSlug, 'Final Test Page', [
        'track_visit' => false,
        'style' => 'retro'
    ]);
    
    echo "<h3>Retro LCD Counter:</h3>\n";
    echo displayRetroCounter($testSlug, [
        'style' => 'lcd',
        'digits' => 6
    ]);
    
    echo "<h3>Modern Style:</h3>\n";
    echo displayVisitorCounter($testSlug, 'Final Test Page', [
        'track_visit' => false,
        'style' => 'modern',
        'show_today_count' => true
    ]);
    
    // Show existing test data
    echo "<h2>Step 7: Existing Test Data</h2>\n";
    $homeStats = $counter->getPageStats('home');
    $testPageStats = $counter->getPageStats('test-page');
    
    if ($homeStats) {
        echo "🏠 Home page: {$homeStats['total_visits']} visits<br>\n";
    }
    if ($testPageStats) {
        echo "🧪 Test page: {$testPageStats['total_visits']} visits<br>\n";
    }
    
    // Clean up test data
    echo "<h2>Step 8: Cleanup</h2>\n";
    $pdo = $db->getPDO();
    $stmt = $pdo->prepare("DELETE FROM aachipsc_blog_page_visits WHERE page_slug = ?");
    $stmt->execute([$testSlug]);
    $stmt = $pdo->prepare("DELETE FROM aachipsc_blog_page_stats WHERE page_slug = ?");
    $stmt->execute([$testSlug]);
    echo "✅ Test data cleaned up<br>\n";
    
    echo "<h2>🎉 All Tests Passed!</h2>\n";
    echo "<div style='background: #e8f5e8; border: 1px solid #4caf50; border-radius: 6px; padding: 15px; margin: 20px 0;'>\n";
    echo "<h3>✅ Visitor Counter System is Working!</h3>\n";
    echo "<p>Your visitor counter is now fully functional. You can:</p>\n";
    echo "<ul>\n";
    echo "<li><strong>View the demo:</strong> <a href='visitor-counter-demo.html'>visitor-counter-demo.html</a></li>\n";
    echo "<li><strong>Test functionality:</strong> <a href='test-visitor-counter.php'>test-visitor-counter.php</a></li>\n";
    echo "<li><strong>Check any page footer</strong> for the live visitor counter</li>\n";
    echo "<li><strong>Customize the display</strong> in includes/footer.php</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>\n";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
    echo "<p>Please check your database connection and try again.</p>\n";
}

echo "<h2>🚀 Next Steps</h2>\n";
echo "<div style='background: #fff3cd; border: 1px solid #ffc107; border-radius: 6px; padding: 15px; margin: 20px 0;'>\n";
echo "<h3>Ready for Production!</h3>\n";
echo "<p>To deploy to your web server:</p>\n";
echo "<ol>\n";
echo "<li><strong>Upload files:</strong> Copy the visitor-counter folder to your web server</li>\n";
echo "<li><strong>Update config:</strong> Change database credentials in secure_config/obq_comments.php</li>\n";
echo "<li><strong>Import SQL:</strong> Run visitor-counter/simple_mariadb.sql on your web server</li>\n";
echo "<li><strong>Test live:</strong> Visit your live site to verify functionality</li>\n";
echo "</ol>\n";
echo "<p><strong>Web server credentials:</strong></p>\n";
echo "<code>\n";
echo "'host' => 'az1-ss110.a2hosting.com',<br>\n";
echo "'dbname' => 'aachipsc_blog',<br>\n";
echo "'username' => 'aachipsc_commentwriter',<br>\n";
echo "'password' => '\$gdIjGDkyFLX',<br>\n";
echo "</code>\n";
echo "</div>\n";

echo "<style>\n";
echo "body { font-family: Arial, sans-serif; max-width: 900px; margin: 0 auto; padding: 20px; }\n";
echo "h1 { color: #333; border-bottom: 2px solid #007cba; }\n";
echo "h2 { color: #555; margin-top: 30px; }\n";
echo "code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }\n";
echo "</style>\n";

// Include visitor counter CSS for proper display
echo "<link rel='stylesheet' href='css/visitor-counter.css'>\n";
?>
