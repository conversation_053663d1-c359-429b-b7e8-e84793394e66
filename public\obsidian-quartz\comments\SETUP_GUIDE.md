# A. A. Chips' Simple Comments System Setup Guide

## Hosting Structure Setup

Your hosting structure should look like this:
```
/
├── public_html/
│   └── obsidian-quartz/          # Your site files (this directory)
│       ├── comments/             # Comment system files
│       ├── content/              # Your blog content
│       ├── css/                  # Stylesheets (includes comments.css)
│       └── ...                   # Other site files
└── secure_config/                # Outside public_html (secure)
    └── obq_comments.php          # Database credentials
```

## Step 1: Configure Database Credentials

1. Edit the file `../secure_config/obq_comments.php` (outside your public_html)
2. Update the following values:

```php
'database' => [
    'host' => 'localhost',                    // Your database host
    'dbname' => 'aachipsc',                  // Your database name
    'username' => 'your_actual_username',    // Replace with your DB username
    'password' => 'your_actual_password',    // Replace with your DB password
    'charset' => 'utf8mb4',
    'table_prefix' => 'aachipsc_blog_',      // Prefix for your tables
],

'auth' => [
    'session_timeout' => 86400 * 7,          // 7 days - how long to remember user info
    'require_email_verification' => false,   // Set to true if you want email verification
],

'admin' => [
    'admin_google_ids' => [],                // Not used in simple auth
    'notification_email' => '<EMAIL>',
],
```

## Step 2: Set Up Database Tables

1. Run the SQL script `aachipsc_comments.sql` in your database
2. This will create the required tables with the prefix `aachipsc_blog_`
3. Your existing `aachipsc_blog_comments` table will be used

## Step 3: Include CSS Styles

1. Add the comments CSS to your pages:
```html
<link rel="stylesheet" href="css/comments.css">
```

## Step 4: Security Settings

1. Generate a random 32-character encryption key
2. Update the `encryption_key` in your secure config
3. Set `secure_cookies` to `true` if using HTTPS (recommended)

## Step 5: Test the Setup

1. Upload all files to your hosting
2. Make sure the secure config file is outside public_html
3. Test database connection by visiting a page with comments
4. Try posting a comment with name and email

## File Permissions

Make sure these files have proper permissions:
- `../secure_config/obq_comments.php` - 600 (read/write for owner only)
- Comment system files - 644 (readable by web server)

## How It Works

1. **Simple Authentication**: Users enter their name and email to comment
2. **Session Management**: User info is remembered for 7 days (configurable)
3. **Spam Protection**: Built-in spam detection and rate limiting
4. **No Registration**: No account creation required - just name and email

## Troubleshooting

### Database Connection Issues
- Check that the secure config path is correct
- Verify database credentials
- Ensure the database user has proper permissions

### Comment Form Issues
- Ensure CSS file is loaded properly
- Check JavaScript console for errors
- Verify all required files are uploaded

### Table Not Found Errors
- Run the SQL script to create tables
- Check table prefix in config matches actual table names
- Verify database name is correct

## Security Notes

- Never put database credentials in public_html
- Use HTTPS for production (recommended)
- Regularly update spam patterns
- Monitor rate limiting logs
- Email addresses are never displayed publicly

## Adding Comments to Posts

To add comments to a blog post, include this PHP code:

```php
<?php
require_once __DIR__ . '/comments/comments-display.php';
echo renderCommentsSection($postSlug); // $postSlug should be unique for each post
?>
```

The `$postSlug` should be a unique identifier for each post (e.g., the filename without extension).
