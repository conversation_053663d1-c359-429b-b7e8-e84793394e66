<?php
$files = scandir(__DIR__);
$listItems = "";

foreach ($files as $file) {
    if (substr($file, -3) === '.md') {
        $content = file_get_contents($file);
        $lines = explode("\n", $content);
        $title = $lines[0];
        $excerpt = "";

        for ($i = 1; $i < count($lines); $i++) {
            if (trim($lines[$i]) === "") {
                break;
            }
            $excerpt .= $lines[$i] . "\n";
        }

        $listItems .= "<li><a href=\"{$file}.php\">{$title}</a><br>\n<p>{$excerpt}</p></li>\n";
    }
}

echo "<ul>\n{$listItems}</ul>\n";
?>

