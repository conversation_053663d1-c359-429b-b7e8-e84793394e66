{"disableDoubleClickTextEditing": false, "folder": "Excalidraw", "cropFolder": "", "annotateFolder": "", "embedUseExcalidrawFolder": false, "templateFilePath": "Excalidraw/Template.excalidraw", "scriptFolderPath": "Excalidraw/Scripts", "fontAssetsPath": "Excalidraw/CJK Fonts", "loadChineseFonts": false, "loadJapaneseFonts": false, "loadKoreanFonts": false, "compress": true, "decompressForMDView": false, "onceOffCompressFlagReset": true, "onceOffGPTVersionReset": true, "autosave": true, "autosaveIntervalDesktop": 60000, "autosaveIntervalMobile": 30000, "drawingFilenamePrefix": "Drawing ", "drawingEmbedPrefixWithFilename": true, "drawingFilnameEmbedPostfix": " ", "drawingFilenameDateTime": "YYYY-MM-DD HH.mm.ss", "useExcalidrawExtension": true, "cropPrefix": "cropped_", "annotatePrefix": "annotated_", "annotatePreserveSize": false, "previewImageType": "SVGIMG", "renderingConcurrency": 3, "allowImageCache": true, "allowImageCacheInScene": true, "displayExportedImageIfAvailable": false, "previewMatchObsidianTheme": false, "width": "400", "height": "", "overrideObsidianFontSize": false, "dynamicStyling": "colorful", "isLeftHanded": false, "iframeMatchExcalidrawTheme": true, "matchTheme": false, "matchThemeAlways": false, "matchThemeTrigger": false, "defaultMode": "normal", "defaultPenMode": "never", "penModeDoubleTapEraser": true, "penModeSingleFingerPanning": true, "penModeCrosshairVisible": true, "renderImageInMarkdownReadingMode": false, "renderImageInHoverPreviewForMDNotes": false, "renderImageInMarkdownToPDF": false, "allowPinchZoom": false, "allowWheelZoom": false, "zoomToFitOnOpen": true, "zoomToFitOnResize": true, "zoomToFitMaxLevel": 2, "linkPrefix": "📍", "urlPrefix": "🌐", "parseTODO": false, "todo": "☐", "done": "🗹", "hoverPreviewWithoutCTRL": false, "linkOpacity": 1, "openInAdjacentPane": true, "showSecondOrderLinks": true, "focusOnFileTab": true, "openInMainWorkspace": true, "showLinkBrackets": true, "allowCtrlClick": true, "forceWrap": false, "pageTransclusionCharLimit": 200, "wordWrappingDefault": 0, "removeTransclusionQuoteSigns": true, "iframelyAllowed": true, "pngExportScale": 1, "exportWithTheme": true, "exportWithBackground": true, "exportPaddingSVG": 10, "exportEmbedScene": false, "keepInSync": false, "autoexportSVG": false, "autoexportPNG": false, "autoExportLightAndDark": false, "autoexportExcalidraw": false, "embedType": "excalidraw", "embedMarkdownCommentLinks": true, "embedWikiLink": true, "syncExcalidraw": false, "experimentalFileType": false, "experimentalFileTag": "✏️", "experimentalLivePreview": true, "fadeOutExcalidrawMarkup": false, "loadPropertySuggestions": true, "experimentalEnableFourthFont": false, "experimantalFourthFont": "<PERSON>", "addDummyTextElement": false, "zoteroCompatibility": false, "fieldSuggester": true, "compatibilityMode": false, "drawingOpenCount": 0, "library": "deprecated", "library2": {"type": "excalidrawlib", "version": 2, "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin/releases/tag/2.12.0", "libraryItems": []}, "imageElementNotice": true, "mdSVGwidth": 500, "mdSVGmaxHeight": 800, "mdFont": "<PERSON>", "mdFontColor": "Black", "mdBorderColor": "Black", "mdCSS": "", "scriptEngineSettings": {}, "defaultTrayMode": true, "previousRelease": "2.12.0", "showReleaseNotes": true, "showNewVersionNotification": true, "latexBoilerplate": "\\color{blue}", "latexPreambleLocation": "preamble.sty", "taskboneEnabled": false, "taskboneAPIkey": "", "pinnedScripts": [], "customPens": [{"type": "default", "freedrawOnly": false, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": 0, "penOptions": {"highlighter": false, "constantPressure": false, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 0.6, "smoothing": 0.5, "streamline": 0.5, "easing": "easeOutSine", "start": {"cap": true, "taper": 0, "easing": "linear"}, "end": {"cap": true, "taper": 0, "easing": "linear"}}}}, {"type": "highlighter", "freedrawOnly": true, "strokeColor": "#FFC47C", "backgroundColor": "#FFC47C", "fillStyle": "solid", "strokeWidth": 2, "roughness": null, "penOptions": {"highlighter": true, "constantPressure": true, "hasOutline": true, "outlineWidth": 4, "options": {"thinning": 1, "smoothing": 0.5, "streamline": 0.5, "easing": "linear", "start": {"taper": 0, "cap": true, "easing": "linear"}, "end": {"taper": 0, "cap": true, "easing": "linear"}}}}, {"type": "finetip", "freedrawOnly": false, "strokeColor": "#3E6F8D", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0.5, "roughness": 0, "penOptions": {"highlighter": false, "hasOutline": false, "outlineWidth": 1, "constantPressure": true, "options": {"smoothing": 0.4, "thinning": -0.5, "streamline": 0.4, "easing": "linear", "start": {"taper": 5, "cap": false, "easing": "linear"}, "end": {"taper": 5, "cap": false, "easing": "linear"}}}}, {"type": "fountain", "freedrawOnly": false, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "roughness": 0, "penOptions": {"highlighter": false, "constantPressure": false, "hasOutline": false, "outlineWidth": 1, "options": {"smoothing": 0.2, "thinning": 0.6, "streamline": 0.2, "easing": "easeInOutSine", "start": {"taper": 150, "cap": true, "easing": "linear"}, "end": {"taper": 1, "cap": true, "easing": "linear"}}}}, {"type": "marker", "freedrawOnly": true, "strokeColor": "#B83E3E", "backgroundColor": "#FF7C7C", "fillStyle": "dashed", "strokeWidth": 2, "roughness": 3, "penOptions": {"highlighter": false, "constantPressure": true, "hasOutline": true, "outlineWidth": 4, "options": {"thinning": 1, "smoothing": 0.5, "streamline": 0.5, "easing": "linear", "start": {"taper": 0, "cap": true, "easing": "linear"}, "end": {"taper": 0, "cap": true, "easing": "linear"}}}}, {"type": "thick-thin", "freedrawOnly": true, "strokeColor": "#CECDCC", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": null, "penOptions": {"highlighter": true, "constantPressure": true, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 1, "smoothing": 0.5, "streamline": 0.5, "easing": "linear", "start": {"taper": 0, "cap": true, "easing": "linear"}, "end": {"cap": true, "taper": true, "easing": "linear"}}}}, {"type": "thin-thick-thin", "freedrawOnly": true, "strokeColor": "#CECDCC", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": null, "penOptions": {"highlighter": true, "constantPressure": true, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 1, "smoothing": 0.5, "streamline": 0.5, "easing": "linear", "start": {"cap": true, "taper": true, "easing": "linear"}, "end": {"cap": true, "taper": true, "easing": "linear"}}}}, {"type": "default", "freedrawOnly": false, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": 0, "penOptions": {"highlighter": false, "constantPressure": false, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 0.6, "smoothing": 0.5, "streamline": 0.5, "easing": "easeOutSine", "start": {"cap": true, "taper": 0, "easing": "linear"}, "end": {"cap": true, "taper": 0, "easing": "linear"}}}}, {"type": "default", "freedrawOnly": false, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": 0, "penOptions": {"highlighter": false, "constantPressure": false, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 0.6, "smoothing": 0.5, "streamline": 0.5, "easing": "easeOutSine", "start": {"cap": true, "taper": 0, "easing": "linear"}, "end": {"cap": true, "taper": 0, "easing": "linear"}}}}, {"type": "default", "freedrawOnly": false, "strokeColor": "#000000", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 0, "roughness": 0, "penOptions": {"highlighter": false, "constantPressure": false, "hasOutline": false, "outlineWidth": 1, "options": {"thinning": 0.6, "smoothing": 0.5, "streamline": 0.5, "easing": "easeOutSine", "start": {"cap": true, "taper": 0, "easing": "linear"}, "end": {"cap": true, "taper": 0, "easing": "linear"}}}}], "numberOfCustomPens": 0, "pdfScale": 4, "pdfBorderBox": true, "pdfFrame": false, "pdfGapSize": 20, "pdfGroupPages": false, "pdfLockAfterImport": true, "pdfNumColumns": 1, "pdfNumRows": 1, "pdfDirection": "right", "pdfImportScale": 0.3, "gridSettings": {"DYNAMIC_COLOR": true, "COLOR": "#000000", "OPACITY": 50, "GRID_DIRECTION": {"horizontal": true, "vertical": true}}, "laserSettings": {"DECAY_LENGTH": 50, "DECAY_TIME": 1000, "COLOR": "#ff0000"}, "embeddableMarkdownDefaults": {"useObsidianDefaults": false, "backgroundMatchCanvas": false, "backgroundMatchElement": true, "backgroundColor": "#fff", "backgroundOpacity": 60, "borderMatchElement": true, "borderColor": "#fff", "borderOpacity": 0, "filenameVisible": false}, "markdownNodeOneClickEditing": false, "canvasImmersiveEmbed": true, "startupScriptPath": "", "aiEnabled": true, "openAIAPIToken": "", "openAIDefaultTextModel": "gpt-3.5-turbo-1106", "openAIDefaultVisionModel": "gpt-4o", "openAIDefaultImageGenerationModel": "dall-e-3", "openAIURL": "https://api.openai.com/v1/chat/completions", "openAIImageGenerationURL": "https://api.openai.com/v1/images/generations", "openAIImageEditsURL": "https://api.openai.com/v1/images/edits", "openAIImageVariationURL": "https://api.openai.com/v1/images/variations", "modifierKeyConfig": {"Mac": {"LocalFileDragAction": {"defaultAction": "image-import", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-import"}, {"shift": true, "ctrl_cmd": false, "alt_opt": true, "meta_ctrl": false, "result": "link"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-url"}, {"shift": false, "ctrl_cmd": false, "alt_opt": true, "meta_ctrl": false, "result": "embeddable"}]}, "WebBrowserDragAction": {"defaultAction": "image-url", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-url"}, {"shift": true, "ctrl_cmd": false, "alt_opt": true, "meta_ctrl": false, "result": "link"}, {"shift": false, "ctrl_cmd": false, "alt_opt": true, "meta_ctrl": false, "result": "embeddable"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-import"}]}, "InternalDragAction": {"defaultAction": "link", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "link"}, {"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": true, "result": "embeddable"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": true, "result": "image-fullsize"}]}, "LinkClickAction": {"defaultAction": "new-tab", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "active-pane"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "new-tab"}, {"shift": false, "ctrl_cmd": true, "alt_opt": true, "meta_ctrl": false, "result": "new-pane"}, {"shift": true, "ctrl_cmd": true, "alt_opt": true, "meta_ctrl": false, "result": "popout-window"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": true, "result": "md-properties"}]}}, "Win": {"LocalFileDragAction": {"defaultAction": "image-import", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-import"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "link"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-url"}, {"shift": true, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "embeddable"}]}, "WebBrowserDragAction": {"defaultAction": "image-url", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-url"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "link"}, {"shift": true, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "embeddable"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image-import"}]}, "InternalDragAction": {"defaultAction": "link", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "link"}, {"shift": true, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "embeddable"}, {"shift": true, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "image"}, {"shift": false, "ctrl_cmd": true, "alt_opt": true, "meta_ctrl": false, "result": "image-fullsize"}]}, "LinkClickAction": {"defaultAction": "new-tab", "rules": [{"shift": false, "ctrl_cmd": false, "alt_opt": false, "meta_ctrl": false, "result": "active-pane"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": false, "result": "new-tab"}, {"shift": false, "ctrl_cmd": true, "alt_opt": true, "meta_ctrl": false, "result": "new-pane"}, {"shift": true, "ctrl_cmd": true, "alt_opt": true, "meta_ctrl": false, "result": "popout-window"}, {"shift": false, "ctrl_cmd": true, "alt_opt": false, "meta_ctrl": true, "result": "md-properties"}]}}}, "slidingPanesSupport": false, "areaZoomLimit": 1, "longPressDesktop": 500, "longPressMobile": 500, "doubleClickLinkOpenViewMode": true, "isDebugMode": false, "rank": "Bronze", "modifierKeyOverrides": [{"modifiers": ["Mod"], "key": "Enter"}, {"modifiers": ["Mod"], "key": "k"}, {"modifiers": ["Mod"], "key": "G"}], "showSplashscreen": true, "pdfSettings": {"pageSize": "A4", "pageOrientation": "portrait", "fitToPage": 1, "paperColor": "white", "customPaperColor": "#ffffff", "alignment": "center", "margin": "normal"}}