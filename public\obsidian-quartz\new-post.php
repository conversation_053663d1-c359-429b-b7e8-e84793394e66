<?php
/**
 * Helper script for creating new blog posts
 * Usage: php new-post.php "Post Title" [category]
 */

if (php_sapi_name() !== 'cli') {
    die("This script must be run from the command line.\n");
}

// Get command line arguments
$title = $argv[1] ?? null;
$category = $argv[2] ?? 'general';

if (!$title) {
    echo "Usage: php new-post.php \"Post Title\" [category]\n";
    echo "Example: php new-post.php \"My New Post\" journal\n";
    echo "\nAvailable categories:\n";
    echo "- advocacy\n";
    echo "- kitchen\n";
    echo "- alienation\n";
    echo "- climate\n";
    echo "- humor\n";
    echo "- inspiration\n";
    echo "- journal\n";
    echo "- personal\n";
    echo "- writings\n";
    exit(1);
}

// Create filename from title
$filename = strtolower(trim($title));
$filename = preg_replace('/[^a-z0-9\s-]/', '', $filename);
$filename = preg_replace('/\s+/', '-', $filename);
$filename = trim($filename, '-');

// Determine directory
$contentDir = 'content';
if ($category !== 'general') {
    $contentDir .= DIRECTORY_SEPARATOR . $category;
    if (!is_dir($contentDir)) {
        mkdir($contentDir, 0755, true);
    }
}

$filepath = $contentDir . DIRECTORY_SEPARATOR . $filename . '.md';

// Check if file already exists
if (file_exists($filepath)) {
    echo "Error: File already exists: $filepath\n";
    exit(1);
}

// Create frontmatter template
$frontmatter = [
    'title' => $title,
    'date' => date('Y-m-d'),
    'author' => 'A. A. Chips',
    'tags' => [$category],
    'excerpt' => 'Add a brief description of your post here...'
];

// Create markdown content
$content = "---\n";
foreach ($frontmatter as $key => $value) {
    if (is_array($value)) {
        $content .= "$key:\n";
        foreach ($value as $item) {
            $content .= "  - $item\n";
        }
    } else {
        $content .= "$key: $value\n";
    }
}
$content .= "---\n\n";
$content .= "# $title\n\n";
$content .= "Write your post content here...\n\n";
$content .= "## Section Heading\n\n";
$content .= "Add more content as needed.\n\n";

// Write file
file_put_contents($filepath, $content);

echo "Created new post: $filepath\n";
echo "Edit the file and then run 'php build.php' to generate the HTML/PHP output.\n";
?>
