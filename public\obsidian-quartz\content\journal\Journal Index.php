<?php
// Auto-generated blog post
// Source: Journal Index.md

// Load configuration
$config = include '../../config.php';

// Page variables
$page_title = 'Journaling Index';
$meta_description = 'Here\'s an index page for past journal entries I am sharing about my story and what I have gone through. I am sharing this intimate view into my past in the hopes that it can help people going through the same thing.';
$meta_keywords = 'journal, april, homeless, addiction, recovery, mystory, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../../css/';
$js_path = '../../js/';
$base_url = '../../';
$related_posts = [
    ['title' => 'Alienation', 'url' => '../../alienation/index.php', 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => '../../climate/index.php', 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => '../../humor/index.php', 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => '../../inspiration/index.php', 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => '../../journal/index.php', 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => '../../judaism/index.php', 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => '../../kitchen/index.php', 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Street', 'url' => '../../street/index.php', 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => '../../writings/index.php', 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Journaling Index',
  '"Date' => '": May 18, 2025',
  'tags' => 
  array (
    0 => 'journal',
    1 => 'april',
    2 => 'homeless',
    3 => 'addiction',
    4 => 'recovery',
    5 => 'mystory',
  ),
  'excerpt' => 'Here\'s an index page for past journal entries I am sharing about my story and what I have gone through. I am sharing this intimate view into my past in the hopes that it can help people going through the same thing.',
  'key' => 'public',
  'source_file' => 'content\\journal\\Journal Index.md',
);

// Raw content
$post_content = '<h3>Me Today</h3>

<ul><li><a href="approach-consumption.php" class="internal-link">approach-consumption</a></li>
<p><li><a href="public/Journal/how-i-sleep-without-a-bed.php" class="internal-link">public/Journal/how i sleep without a bed</a></li></p>
<p><li><a href="i-am-not-smee.php" class="internal-link">I\'ve got to address something. I was told today I was giving off heavy Mr. Smee vibes, as in the supporting antagonist from Hook. I want to put on record, that I look nothing like Mr. Smee. I had a picture taken to prove how little of a resemblance there is. I am not Smee.</a> </li></p>
<p><li></li></p>
<p><li><a href="i-am-testing-for-my-cpacc-certification-this-is-what-that-means.php" class="internal-link">I am testing for my CPACC Certification. This is what that means.</a></li></p>
<p><li><a href="public/Journal/i-cook-most-of-my-meals.php" class="internal-link">public/Journal/I cook most of my meals</a></li></p>
<p><li><a href="dont-help-my-wife.php" class="internal-link">dont-help-my-wife</a></li></p>
<p><li><a href="i-dont-want-to-talk-for-the-rest-of-my-life.php" class="internal-link">I dont want to talk for the rest of my life</a></li></p>
<p><li><a href="left-not-coming-back.php" class="internal-link">left-not-coming-back</a></li></p>
<p><li><a href="i-like-my-space-but-someone-or-someones-need-me-to-show-up-in-the-world-more.php" class="internal-link">I like my space, but someone or someones need me to show up in the world more</a></li></p>
<p><li><a href="i-m-scared-of-losing-my-bullshit-job.php" class="internal-link">I\'m scared of losing my Bullshit Job</a></li></p>

<h3>General</h3>

<p><li><a href="homelessness-timeline.php" class="internal-link">Homelessness Timeline</a></li></p>
<p><li><a href="how-i-became-homeless.php" class="internal-link">How I became Homeless</a></li></p>
<p><li><a href="i-was-homeless-between-2014-and-2019.php" class="internal-link">I was homeless between 2014 and 2019</a></li></p>
<p><li><a href="paradox-of-age.php" class="internal-link">paradox-of-age</a></li></p>
<p><li><a href="nostalgia-a-poem.php" class="internal-link">Nostalgia - A poem</a></li></p>
<h2>Love Story of my Life</h2>

<p><a href="edge-of-illusion.php" class="internal-link">Edge of Illusion</a></p>
<p><a href="failing-upward-into-a-haunted-college.php" class="internal-link">Failing Upward into a Haunted College</a></p>
<p><a href="ridges-a-poem.php" class="internal-link">Ridges - A Poem</a></p>
<p><a href="senior-year-how-to-fail-your-way-to-freedom.php" class="internal-link">Senior Year -- How to Fail your Way to Freedom</a></p>
<p><a href="sophomore-year-high-school-halloween.php" class="internal-link">Sophomore Year, High School -- Halloween</a></p>

<p>2014</p>
<p><a href="climate-vault.php" class="internal-link">Here is an Index vault about advocacy and walking across America in 2014 for global climate action.</a></p>
<p><a href="29-palms.php" class="internal-link">29-palms</a></p>

<p>2016</p>
<p><li><a href="rv-walmart.php" class="internal-link">This is a journal entry I wrote back in December of 2016 when I was ridesharing south to North Carolina, and then Florida. I rode with a great man and his young son in their RV, and when we stopped at the Walmart in Johnson City, Tennessee to rest at night, we woke up to a 20 degree snowstorm that blocked us in for over a day. The fuse for the heater in the RV also broke, which meant we had to run the engine to stay warm.</a></li></p>

<p><li><a href="standing-rock-stories.php" class="internal-link">Standing Rock Stories</a></li></p>
<p><li><a href="public/Journal/standing-rock-vault.php" class="internal-link">public/Journal/Standing Rock Vault</a></li></p>


<p>2017</p>

<p><li><a href="first-night-stealth-camping.php" class="internal-link">first-night-stealth-camping</a></li></p>
<p><li><a href="seeking-refuge.php" class="internal-link">Seeking Refuge</a></li></p>
<p><li><a href="before-inaugural.php" class="internal-link">before-inaugural</a></li></p>
<p><li><a href="day-in-the-life-2017.php" class="internal-link">day-in-the-life-2017</a></li></p>
<p><li><a href="july-4th-asthma-attack.php" class="internal-link">July 4th Asthma Attack</a></li></p>
<p><li><a href="what-have-i-gotten-myself-into.php" class="internal-link">what-have-i-gotten-myself-into</a></li></p>
<p><li><a href="living-in-suv-means-no-indoor-plumbing.php" class="internal-link">Living in SUV Means no Indoor Plumbing</a></li></p>
<p><li><a href="car-partment.php" class="internal-link">car-partment</a></li></ul></p>





<p><a href="instacart-shopping.php" class="internal-link">instacart-shopping</a></p>
<p><a href="my-car-s-journey.php" class="internal-link">My Car\'s Journey</a></p>
<p><a href="my-reading-journey.php" class="internal-link">My Reading Journey</a></p>


<p><a href="sleepless-in-pain.php" class="internal-link">sleepless-in-pain</a></p>


<p><a href="power-returns-helene.php" class="internal-link">power-returns-helene</a></p>



<p>2025</p>
<p><a href="live-my-life.php" class="internal-link">live-my-life</a></p>


<p><a href="the-last-night-before-they-leave.php" class="internal-link">The Last Night Before They Leave</a></p>


<img src="img/applepie.gif" alt="applepie.gif">
<img src="img/badBreathing.png" alt="badBreathing.png">
<img src="img/carbookshelf1.jpg" alt="carbookshelf1.jpg">


<img src="img/messycar.jpg" alt="messycar.jpg">

<img src="img/carbookshelf.jpg" alt="carbookshelf.jpg">

<img src="img/car-bookshelf.jpg" alt="car-bookshelf.jpg">
<img src="img/bag-rope.jpg" alt="bag-rope.jpg">
<img src="img/bucket-toilet.jpg" alt="bucket-toilet.jpg"> - August 6th 2017


<img src="img/car-bed.jpg" alt="car-bed.jpg">
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../../page template.htm';
?>