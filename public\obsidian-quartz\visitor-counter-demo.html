<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visitor Counter Demo - A. A. Chips' Blog</title>
    <link rel="stylesheet" href="css/visitor-counter.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007cba;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
        }
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 6px;
            border-left: 4px solid #007cba;
        }
        .note {
            background: #e8f4f8;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Visitor Counter Demo</h1>
        
        <div class="note">
            <strong>Note:</strong> This is a static demo showing how the visitor counters will look once the database is set up. 
            The actual counters will display real visit data from your blog.
        </div>

        <h2>1. Default Retro Style (Footer Display)</h2>
        <div class="demo-section">
            <p>This is how the counter appears in your page footer:</p>
            
            <div class="visitor-counter visitor-counter-retro">
                <div class="page-counter">
                    <span class="counter-label">This page:</span> 
                    <span class="counter-number">1,247</span> visits
                </div>
                <div class="site-counter">
                    <span class="counter-label">Total site visits:</span> 
                    <span class="counter-number">15,892</span>
                </div>
            </div>
        </div>

        <h2>2. Retro LCD Counter (Classic Green)</h2>
        <div class="demo-section">
            <p>Classic LCD-style counter with individual digit displays:</p>
            
            <div class="retro-counter retro-counter-lcd">
                <div class="counter-label">Page Visitors:</div>
                <div class="counter-display">
                    <span class="counter-digit">0</span>
                    <span class="counter-digit">0</span>
                    <span class="counter-digit">1</span>
                    <span class="counter-digit">2</span>
                    <span class="counter-digit">4</span>
                    <span class="counter-digit">7</span>
                </div>
            </div>
        </div>

        <h2>3. Retro LED Counter (Classic Red)</h2>
        <div class="demo-section">
            <p>LED-style counter with red digits:</p>
            
            <div class="retro-counter retro-counter-led">
                <div class="counter-label">Total Site Visitors:</div>
                <div class="counter-display">
                    <span class="counter-digit">0</span>
                    <span class="counter-digit">1</span>
                    <span class="counter-digit">5</span>
                    <span class="counter-digit">8</span>
                    <span class="counter-digit">9</span>
                    <span class="counter-digit">2</span>
                </div>
            </div>
        </div>

        <h2>4. Mechanical Counter Style</h2>
        <div class="demo-section">
            <p>Physical counter-style display:</p>
            
            <div class="retro-counter retro-counter-mechanical">
                <div class="counter-label">Page Visitors:</div>
                <div class="counter-display">
                    <span class="counter-digit">0</span>
                    <span class="counter-digit">0</span>
                    <span class="counter-digit">1</span>
                    <span class="counter-digit">2</span>
                    <span class="counter-digit">4</span>
                    <span class="counter-digit">7</span>
                </div>
            </div>
        </div>

        <h2>5. Modern Style</h2>
        <div class="demo-section">
            <p>Contemporary gradient styling:</p>
            
            <div class="visitor-counter visitor-counter-modern">
                <div class="page-counter">
                    <span class="counter-label">This page:</span> 
                    <span class="counter-number">1,247</span> visits
                    <span class="today-count">(23 today)</span>
                </div>
                <div class="site-counter">
                    <span class="counter-label">Total site visits:</span> 
                    <span class="counter-number">15,892</span>
                    <span class="today-count">(156 today)</span>
                </div>
            </div>
        </div>

        <h2>6. Minimal Style</h2>
        <div class="demo-section">
            <p>Clean, simple design:</p>
            
            <div class="visitor-counter visitor-counter-minimal">
                <div class="page-counter">
                    <span class="counter-label">This page:</span> 
                    <span class="counter-number">1,247</span> visits
                </div>
                <div class="site-counter">
                    <span class="counter-label">Total site visits:</span> 
                    <span class="counter-number">15,892</span>
                </div>
            </div>
        </div>

        <h2>7. Compact Display</h2>
        <div class="demo-section">
            <p>Inline compact format:</p>
            
            <div class="visitor-counter-compact">
                <span class="page-count">1,247</span> | <span class="site-count">15,892</span>
            </div>
        </div>

        <h2>🔧 How to Set Up</h2>
        <div class="demo-section">
            <h3>Step 1: Start MySQL</h3>
            <p>Make sure your XAMPP MySQL server is running. You can:</p>
            <ul>
                <li>Use XAMPP Control Panel and click "Start" next to MySQL</li>
                <li>Or run the manual setup commands in the guide</li>
            </ul>

            <h3>Step 2: Import Database Schema</h3>
            <p>Run the SQL file to create the visitor counter tables:</p>
            <div class="code">
                Import: visitor-counter/visitor_counter.sql
            </div>

            <h3>Step 3: Test the System</h3>
            <p>Visit your test page to see the counters in action:</p>
            <div class="code">
                your-site.com/test-visitor-counter.php
            </div>

            <h3>Step 4: Customize</h3>
            <p>Edit the footer display options in <code>includes/footer.php</code>:</p>
            <div class="code">
                'style' => 'retro',           // Change to 'modern' or 'minimal'<br>
                'show_today_count' => true,   // Show today's visit counts<br>
                'format' => 'compact',        // Use compact display
            </div>
        </div>

        <h2>📊 Features</h2>
        <div class="demo-section">
            <ul>
                <li><strong>Smart Deduplication:</strong> Only counts one visit per IP per day per page</li>
                <li><strong>Privacy Friendly:</strong> No personal data stored, only hashed user agents</li>
                <li><strong>Performance Optimized:</strong> Uses aggregated statistics for fast display</li>
                <li><strong>Multiple Styles:</strong> Retro, modern, and minimal display options</li>
                <li><strong>Responsive Design:</strong> Works on all device sizes</li>
                <li><strong>Easy Customization:</strong> Simple configuration options</li>
            </ul>
        </div>

        <div class="note">
            <strong>Next Steps:</strong> Follow the manual setup guide in <code>visitor-counter/MANUAL_SETUP.md</code> 
            to get your MySQL database running and import the visitor counter tables. Once that's done, 
            your site will have working visitor counters with that classic retro web aesthetic!
        </div>
    </div>
</body>
</html>
