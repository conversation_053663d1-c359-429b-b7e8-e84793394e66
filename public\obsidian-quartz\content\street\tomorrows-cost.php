<?php
// Auto-generated blog post
// Source: tomorrows-cost.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Secret Agent 23 Skidoo - Tomorrow\'s Cost';
$meta_description = '\'I hope this post gets shared nationally, because I know the story of Hurricane <PERSON><PERSON> and the devastation it wrought on the communities of Western North Carolina is getting washed away by the flood of events in the news cycle.\'';
$meta_keywords = 'homeless, disasterrelief, music, advocacy, blog, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fwww.severe-weather.eu%2Fwp-content%2Fgallery%2Fandrej-news%2Fhurricane-season-2024-forecast-major-storm-helene-florida-us-landfall.jpg&f=1&nofb=1&ipt=d693aff8790d872b9096a555c2a6e40c3a17d5c13a91e3280aca25504998331d';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Secret Agent 23 Skidoo - Tomorrow\'s Cost',
  'author' => 'Secret Agent 23 Skidoo',
  'excerpt' => '\'I hope this post gets shared nationally, because I know the story of Hurricane Helene and the devastation it wrought on the communities of Western North Carolina is getting washed away by the flood of events in the news cycle.\'',
  'date' => '2024-11-10',
  'tags' => 
  array (
    0 => 'homeless',
    1 => 'disasterrelief',
    2 => 'music',
    3 => 'advocacy',
    4 => 'blog',
  ),
  'thumbnail' => 'https://external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fwww.severe-weather.eu%2Fwp-content%2Fgallery%2Fandrej-news%2Fhurricane-season-2024-forecast-major-storm-helene-florida-us-landfall.jpg&f=1&nofb=1&ipt=d693aff8790d872b9096a555c2a6e40c3a17d5c13a91e3280aca25504998331d',
  'source_file' => 'content\\street\\tomorrows-cost.md',
);

// Raw content
$post_content = '<iframe width="560" height="315" src="https://www.youtube.com/embed/TS5C6IqLSgs?si=x9vokJjDkm5SOsg4" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>


<h3><a href="https://www.facebook.com/profile.php?id=100007118299223&__cft__[0]=AZWcKjZBy7SE6J4p8iT1bXw1M6eDbcgHktWntrA_ED-_k_HGbc1uUxnRAqxRpQQRjvzSmG2fwS9-EqNZr7WjrlGh09UfBc3RiRAAN5rO2q9FYeZ6SmBBlyawx31slHJS9PdTexmAU_VaG6MV4l9JIo0fF2ddqWQ4kIyW_GNX8a10VP_-NvxNl2wEuCE3ChLnaag0RHZpAMLiqJ2Tw0XeMHjK&__tn__=-UC%2CP-y-R" class="external-link"><strong>Twentythree Skidoo</strong></a></h3>

<p>I hope this post gets shared nationally, because I know the story of Hurricane Helene and the devastation it wrought on the communities of Western North Carolina is getting washed away by the flood of events in the news cycle.</p>

<p>But for those of us in Asheville and WNC, the storm is far from over. Although the city water is back on, we can\'t drink it, and it\'s even dicey to shower. So many folks are without homes now, lost to the floods, the thousands of falling trees, the landslides, and the mold. So many artists, musicians, service workers, and countless others have little to no income....and now we\'re headed into winter.</p>

<p>Take a look, and a listen. This song and video are meant to encapsulate what we went through, and where we\'re at now. If it moves you, you can help us out at either <a href="https://www.facebook.com/BeLovedAsheville?__cft__[0]=AZWcKjZBy7SE6J4p8iT1bXw1M6eDbcgHktWntrA_ED-_k_HGbc1uUxnRAqxRpQQRjvzSmG2fwS9-EqNZr7WjrlGh09UfBc3RiRAAN5rO2q9FYeZ6SmBBlyawx31slHJS9PdTexmAU_VaG6MV4l9JIo0fF2ddqWQ4kIyW_GNX8a10VP_-NvxNl2wEuCE3ChLnaag0RHZpAMLiqJ2Tw0XeMHjK&__tn__=-]K-y-R" class="external-link">BeLoved Asheville</a> (dot com), one of the most trusted organizations serving WNC in so many ways, or at FloodBackArt (dot com), a directory of local artist that could really use your attention. Buy their art, or donate to their causes. (Links in comments)</p>

<p>For Christmas this year, tell your loved ones the story of how you helped folks that are really going through it, and did it in their names! What a gift that could be, to them and to us. Thanks, yall!</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>