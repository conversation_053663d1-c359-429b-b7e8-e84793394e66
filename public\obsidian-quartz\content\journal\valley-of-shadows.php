<?php
// Auto-generated blog post
// Source: valley-of-shadows.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '\'Free from the stories I\'ve been told, I walk through the valley of my own shadows\' - Morning Journal 5-22-2025';
$meta_description = 'Her mother carried homelessness like a stone in her pocket, fingering its edges until they drew blood. "It’s hard for a parent," Mera says. But she doesn’t flinch from the truth: "Harder for the one sleeping in the street."';
$meta_keywords = 'homeless, journal, advocacy, alienation, writings, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '\'Free from the stories I\'ve been told, I walk through the valley of my own shadows\' - Morning Journal 5-22-2025',
  'date' => '2025-05-22',
  'excerpt' => 'Her mother carried homelessness like a stone in her pocket, fingering its edges until they drew blood. "It’s hard for a parent," Mera says. But she doesn’t flinch from the truth: "Harder for the one sleeping in the street."',
  'tags' => 
  array (
    0 => 'homeless',
    1 => 'journal',
    2 => 'advocacy',
    3 => 'alienation',
    4 => 'writings',
  ),
  'source_file' => 'content\\journal\\valley-of-shadows.md',
);

// Raw content
$post_content = '<p>Mera knocks softly at my door just after dawn. We’ve been waking like this all week, drawn into conversation by the weight of dream fragments still clinging to us. Six years ago, I found her wallet in the church kitchen where I packed COVID food boxes. Back then, she was a ghost—hollow-eyed, living in a storage unit, her body a battleground for addiction and violence. Now, she’s solid. Clean. Sitting cross-legged on my floor in acrylic painted sweatpants, her hair a messy crown under the hallway Christmas lights.</p>

<h3>We talk about mothers.</h3>

<p>Her mother carried homelessness like a stone in her pocket, fingering its edges until they drew blood. _"It’s hard for a parent,"_ Mera says. But she doesn’t flinch from the truth: "It\'s ten times harder for the person sleeping outside, on concrete, in a storage unit, or in a car." I think of Gaza, of mass starvation—how bearing witness is not the same as being buried in the rubble. Understanding came for Mera’s mother only when she saw the cracks in her daughter’s hands, the way the streets had carved into her.</p>

<p>My own mother? I keep the door locked. Not out of cruelty, but survival. I know her rhythms too well: the police calls, the tears, the way her stories warp reality until someone else pays the price. In her presence, I imagine sirens, a gun misfired, a friend’s body crumpling under the weight of her narrative.</p>

<p>That’s why I’m building this site. Not to plead my case, not to chase rumors—but to lay the truth bare, like a bone on a table. Let them think what they want. But for the ones still listening, I’ll say it plain: _This is what it looks like from the ground._</p>

<img src="../../img/standingbear.jpg" alt="i did not raise you to see you live with fear. Strike it from your heart. Do not bring it into our village." width="400">

<h3>Related:</h3>

<p><a href="../street/when-choice-isnt-choice.php">When "Choice" Isn’t Really a Choice</a></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>