# Manual Visitor Counter Setup Guide

Since the automated setup script encountered database connection issues, here's how to manually set up the visitor counter system.

## Prerequisites

1. **XAMPP/MySQL Running**: Make sure your MySQL server is running
2. **Database Access**: Ensure you can connect to your `aachipsc` database
3. **PHP Extensions**: Ensure PDO and PDO_MySQL are enabled

## Step 1: Start MySQL Server

### Option A: Using XAMPP Control Panel
1. Open XAMPP Control Panel
2. Click "Start" next to MySQL
3. Wait for it to show "Running" status

### Option B: Command Line (if XAMPP Control Panel doesn't work)
```bash
# Navigate to XAMPP directory
cd C:\xampp2

# Start MySQL service
.\mysql_start.bat
```

### Option C: Repair Database (if corrupted)
If you see Aria recovery errors:
```bash
cd C:\xampp2\mysql\bin
.\aria_chk.exe -r C:\xampp2\mysql\data\*.MAI
```

## Step 2: Create Database Tables

1. **Open phpMyAdmin** or your preferred MySQL client
2. **Select your database** (usually `aachipsc`)
3. **Run the SQL script** from `visitor-counter/visitor_counter.sql`

### Manual SQL Execution:
```sql
-- Copy and paste the contents of visitor_counter.sql
-- Or import the file directly in phpMyAdmin
```

## Step 3: Verify Installation

### Check if tables were created:
```sql
SHOW TABLES LIKE 'aachipsc_blog_%';
```

You should see these tables:
- `aachipsc_blog_page_visits`
- `aachipsc_blog_page_stats`
- `aachipsc_blog_site_stats`
- `aachipsc_blog_visitor_sessions`
- `aachipsc_blog_daily_stats`

## Step 4: Test the System

1. **Visit your test page**: `your-site.com/test-visitor-counter.php`
2. **Check the footer** of any page for the visitor counter
3. **Refresh pages** to see if counts increment (should only increment once per day per IP)

## Step 5: Customize Display

### Edit Footer Display
In `includes/footer.php`, you can change the counter options:

```php
echo displayVisitorCounter($currentPageSlug, $page_title ?? null, [
    'style' => 'retro',           // 'retro', 'modern', 'minimal'
    'show_page_count' => true,    // Show page-specific count
    'show_site_count' => true,    // Show site-wide count
    'show_today_count' => false,  // Show today's counts
    'format' => 'full'            // 'full', 'compact', 'numbers-only'
]);
```

### Available Styles:
- **retro**: Classic green LCD look
- **modern**: Gradient styling
- **minimal**: Clean, simple design

### Retro Counter Variants:
```php
// LCD style (green)
echo displayRetroCounter($pageSlug, ['style' => 'lcd']);

// LED style (red)
echo displayRetroCounter($pageSlug, ['style' => 'led']);

// Mechanical style
echo displayRetroCounter($pageSlug, ['style' => 'mechanical']);
```

## Troubleshooting

### Database Connection Issues
1. **Check credentials** in `../secure_config/obq_comments.php`
2. **Verify MySQL is running** on port 3306
3. **Test connection** with a simple PHP script:

```php
<?php
try {
    $pdo = new PDO("mysql:host=localhost;dbname=aachipsc", $username, $password);
    echo "Database connection successful!";
} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage();
}
?>
```

### Counter Not Displaying
1. **Check CSS inclusion** in page template
2. **Verify file paths** in footer.php
3. **Check PHP errors** in error logs
4. **Ensure database tables exist**

### Counts Not Incrementing
1. **Check for PHP errors** in logs
2. **Verify database permissions**
3. **Test with different IP addresses**
4. **Check if visits are being recorded** in `aachipsc_blog_page_visits` table

## Files Overview

### Core Files:
- `visitor-counter/VisitorCounter.php` - Main counter class
- `visitor-counter/visitor-display.php` - Display functions
- `css/visitor-counter.css` - Styling
- `visitor-counter/visitor_counter.sql` - Database schema

### Integration Files:
- `page template.htm` - CSS inclusion
- `includes/footer.php` - Counter display
- `test-visitor-counter.php` - Test page

## Database Schema Summary

### Main Tables:
1. **page_visits** - Individual visit records
2. **page_stats** - Aggregated page statistics
3. **site_stats** - Site-wide statistics
4. **visitor_sessions** - Unique visitor tracking
5. **daily_stats** - Daily trend data

### Key Features:
- **Duplicate prevention** - One visit per IP per day per page
- **Privacy protection** - Hashed user agents, no personal data
- **Performance optimization** - Aggregated stats for fast display
- **Flexible display** - Multiple styles and formats

## Next Steps

1. **Start MySQL server**
2. **Import the SQL schema**
3. **Test the functionality**
4. **Customize the display to your liking**
5. **Monitor the visitor statistics**

The visitor counter will give your site that classic retro feel while providing modern, accurate analytics!
