<?php
/**
 * Root redirect handler for obsidian-quartz
 * Redirects visitors from the root directory to content/index.php
 * This ensures proper path resolution for images and links
 */

// Check if we're being accessed directly (not through a redirect)
$requestUri = $_SERVER['REQUEST_URI'] ?? '';
$scriptName = $_SERVER['SCRIPT_NAME'] ?? '';

// Get the base path of the obsidian-quartz directory
$basePath = dirname($scriptName);
if ($basePath === '/') {
    $basePath = '';
}

// Redirect to content/index.php with proper path handling
$redirectUrl = $basePath . '/content/index.php';

// Use 302 (temporary) redirect to avoid caching issues during development
header("Location: $redirectUrl", true, 302);
exit();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A. A. Chips' Blog - Redirecting...</title>
    <meta name="description" content="Personal stories, advocacy work, and reflections on homelessness, family alienation, and rebuilding life. Join me on this journey of expression and connection.">
    <meta name="keywords" content="A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life">
    <meta name="author" content="A. A. Chips">
    <meta name="robots" content="index, follow">
    
    <!-- Fallback redirect for browsers that don't support PHP redirects -->
    <meta http-equiv="refresh" content="0; url=content/index.php">
    
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background-color: #f5f5f5;
        }
        .redirect-message {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .redirect-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .redirect-link:hover {
            background-color: #005a87;
        }
    </style>
</head>
<body>
    <div class="redirect-message">
        <h1>Welcome to A. A. Chips' Digital Garden</h1>
        <p>You are being redirected to the main site...</p>
        <p>If you are not redirected automatically, please <a href="content/index.php" class="redirect-link">click here</a>.</p>
        
        <script>
            // JavaScript redirect as additional fallback
            setTimeout(function() {
                window.location.href = 'content/index.php';
            }, 100);
        </script>
    </div>
</body>
</html>
