<?php
// Auto-generated blog post
// Source: 100-things-about-myself.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '100 Things I Know About Myself';
$meta_description = 'This was an exercise for our stand up comedy support group. I hope reading through it doesn\'t cause you to eat rocks.';
$meta_keywords = 'personal, writings, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../img/lurkLaughLoathe.png';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '100 Things I Know About Myself',
  'author' => 'A. A. Chips',
  'excerpt' => 'This was an exercise for our stand up comedy support group. I hope reading through it doesn\'t cause you to eat rocks.',
  'categories' => 
  array (
    0 => 'Personal Reflections',
  ),
  'tags' => 
  array (
    0 => 'personal',
    1 => 'writings',
  ),
  'thumbnail' => '../../img/lurkLaughLoathe.png',
  'source_file' => 'content\\100-things-about-myself.md',
);

// Raw content
$post_content = '<img src="../img/lurkLaughLoathe.png" alt="Lurk Laugh Loathe.">


<p>1) I don\'t ever know how I am. If you ask me how I am doing, I will tell you that \'I don\'t know, I don\'t really keep track\'.</p>
<p>2) I really like eating. It feels weird.</p>
<p>3) I drive like a grandma. That is, five under the speed limit and many eight-point-turnarounds.</p>
<p>4) I have been living sobriety for two years.</p>
<p>5) I need a lot of sleep to function.</p>
<p>6) I don\'t really enjoy talking. I do it to get by.</p>
<p>7) I\'m a solid 3.5/5 star cook. But my culinary planning and kitchen skills are 5/5.</p>
<p>8) I make cool snacks and cool facts.</p>
<p>9) I am the inventor of Chiptocurrency, the edible money of the future.</p>
<p>10) I am not racist, but, I think mayonnaise is a modern culinary marvel.</p>
<p>11) I\'m married to a ghost. She takes over sometimes.</p>
<p>12) Through lucid dreaming, I have a great sex life, despite being involuntarily celibate in the waking realm.</p>
<p>13) Ever since the toilet paper scares of 2020, I clean my behind with a squeeze bottle originally intended for barbecue sauce.</p>
<p>14) I absolutely hate mattresses and think they are the scourge of the earth.</p>
<p>15) I sleep in a hammock as many nights as possible.</p>
<p>16) Everytime something terrible happens to me I come up with an idea for a mobile application that would prevent that thing from happening to someone else.</p>
<p>17) I\'ve been staying sober for two years out of spite, in order to be more dangerous at the things I care about.</p>
<p>18) I got my car for free by being a storage doula in Asheville.</p>
<p>19) I hate smart phones and the way they make my pudgey fingers feel. Less screens, more levers, knobs, and buttons.</p>
<p>20) I pee multiple times every night.</p>
<p>21) I have peeing dreams, of perpetual broken restrooms, and people watching me ready to make an arrest. By the will of a higher power I don\'t ever pee while sleeping.</p>
<p>22) I don\'t really like restaurants or eating out.</p>
<p>23) I\'m disinherited from the will.</p>
<p>24) I am anti-therapy. I think it\'s an insurance pyramid scheme.</p>
<p>25) I have a degree in Behavioral Psychology.</p>
<p>26) I walked across America once.</p>
<p>27) I don\'t like walking anymore.</p>
<p>28) I can live off rice and beans and peanut butter and jelly</p>
<p>29) If I was a cat, I would be down about five of my nine lives.</p>
<p>30) My one sibling is the bane of my existence.</p>
<p>31) My method of conflict resolution is to bottle it up and turn that dysfunction into content.</p>
<p>32) My cat is a high functioning alcoholic.</p>
<p>33) I am always accepting cash and money. I am ready for the universe to give me $50,000 with no strings attached.</p>
<p>34) I love my semitic peoples. Especially the ones getting massacred by Israel.</p>
<p>35) My form of witchcraft is making compelling websites for ideas and projects.</p>
<p>36) Don\'t burn white sage or palo santo around me. I really don\'t like when people culturally appropriate around me, and also it makes my forehead burn and for me to speak in tongues.</p>
<p>37) I can\'t be bought off, but I can be bribed with gifts of Chinese takeout.</p>
<p>38) At any time there are dozens of bugs and worms in my brain causing cognitive deficit, and all of them are valid and loved.</p>
<p>39) There was nothing significant altered about my life from the storm, and also I was not helpful with the mass grassroots relief organizing that happened.</p>
<p>40) When something terrible happens, I retreat into myself, consume as little as possible, avoid getting in the way, and take my time to get regrouped.</p>
<p>41) I used to be homeless mainly between the time of 2014 and 2019. This looked like walking across America in the beginning, to living between my car and peoples couches, to freaking out on my parent\'s behavior, and running away with my car seeking refuge to a new city and state. If you want to learn more about why I made that decision, what my life has been like, or about the street advocacy that I do, then I welcome you to follow the links.</p>
<p>42) I advocate for reforms and structural changes in mental health, social work, peer support, housing for all, and our food system. I have a lot of writings on here.</p>
<p>43) I\'ve been going back to school the past three years and learning how to code websites and applications, and have some really cool projects I\'d love for you to check out.</p>
<p>44) I can speak Jewish Spanish pretty well. I\'d like to teach others how to speak Spanish the Jewish way.</p>
<p>45) I\'m not on good terms with my family of upbringing.</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>