<?php
// Auto-generated blog post
// Source: left-not-coming-back.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'I have left and will not be returning - August 2017';
$meta_description = 'I have left and will not be returning. This is difficult to say, but I need to be in the mountains. I require a sense of agency and connection to the unconventional. I am in need of support and friendship – someone who encourages my positive qualities.';
$meta_keywords = 'homeless, journal, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../img/self/running away.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'I have left and will not be returning - August 2017',
  'date' => '2017-08-04',
  'categories' => 
  array (
    0 => 'Journal',
  ),
  'author' => 'A. A. Chips',
  'excerpt' => 'I have left and will not be returning. This is difficult to say, but I need to be in the mountains. I require a sense of agency and connection to the unconventional. I am in need of support and friendship – someone who encourages my positive qualities.',
  'tags' => 
  array (
    0 => 'homeless',
    1 => 'journal',
  ),
  'thumbnail' => '../../img/self/running away.jpg',
  'source_file' => 'content\\journal\\left-not-coming-back.md',
);

// Raw content
$post_content = '<p>I have left and will not be returning. This is difficult to say, but I need to be in the mountains. I require a sense of agency and connection to the unconventional. I am in need of support and friendship – someone who encourages my positive qualities.</p>

<p>I don\'t believe medication or psychiatric intervention are what I need right now. I need to disconnect and be close to the mountains, drinking from natural springs. I need to be in a place where I\'m not the only young person actively involved and where my sexuality isn\'t a source of isolation or taboo.</p>

<p>This move is about reconnecting with my younger self, offering him support and a safe path forward so he can open up when he feels ready.</p>

<p>I am doing well. I will no longer post publicly on Facebook. I have chosen to leave and will openly ask for financial assistance. If this concerns you, please discuss it with me directly. My physical and emotional well-being have suffered from stagnation and family stress. I am currently living in a tiny home in North Carolina and volunteering in food recovery as much as I can. I want to respect the land where I live and acknowledge the struggles of our time.</p>

<p>For a long time, I questioned my own sanity, even believing I might be a pathological liar, influenced by her constant dishonesty and expectation of my truthfulness. I realize that remaining in close contact would lead me down the same path.</p>

<p>I acknowledge that I left and cut off communication out of fear and a need to establish boundaries. I am emotionally scarred and lack trust in my mother. I am afraid of who she is now, based on who she was a decade ago. Your anger is understandable; I am troubled by the consequences of my decisions, as well as larger issues like the fallout from gas drilling (we successfully banned fracking in Maryland) and the heartbreaking case of my best friend\'s trafficked child. It is easier to direct anger at a loved one who disappears than at the underlying problems. Expressing myself in short online posts is difficult and time-consuming. The message "Let me be dead to you" was sent during a breakdown while sending a minimal birthday card. Moving forward, I will not send anything out of fear, obligation, or guilt, as it is unhealthy for everyone. I sent Ellie a gift through Steve to let her know I love and miss her.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>