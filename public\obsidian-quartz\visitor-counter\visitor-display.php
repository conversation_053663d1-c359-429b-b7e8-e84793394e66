<?php
/**
 * Visitor Counter Display Component
 * For A. A. Chips' Obsidian-Quartz Blog
 * 
 * Provides easy-to-use functions for displaying visitor counts
 * with retro styling reminiscent of old website counters.
 */

require_once __DIR__ . '/VisitorCounter.php';

/**
 * Display visitor counter for the current page
 * 
 * @param string $pageSlug The page identifier (auto-detected if not provided)
 * @param string $pageTitle The page title (auto-detected if not provided)
 * @param array $options Display and tracking options
 * @return string HTML output
 */
function displayVisitorCounter($pageSlug = null, $pageTitle = null, $options = []) {
    try {
        // Auto-detect page slug if not provided
        if (!$pageSlug) {
            $pageSlug = getPageSlugFromRequest();
        }
        
        // Auto-detect page title if not provided
        if (!$pageTitle && isset($GLOBALS['page_title'])) {
            $pageTitle = $GLOBALS['page_title'];
        }
        
        // Default options
        $defaultOptions = [
            'track_visit' => true,          // Whether to record this visit
            'show_page_count' => true,      // Show page-specific count
            'show_site_count' => true,      // Show site-wide count
            'show_today_count' => false,    // Show today's counts
            'style' => 'retro',             // Style theme (retro, modern, minimal)
            'format' => 'full',             // Display format (full, compact, numbers-only)
            'cache_duration' => 300,        // Cache duration in seconds (5 minutes)
            'referrer' => $_SERVER['HTTP_REFERER'] ?? null
        ];
        
        $options = array_merge($defaultOptions, $options);
        
        // Initialize visitor counter
        $counter = new VisitorCounter();
        
        // Record the visit if enabled
        if ($options['track_visit'] && $pageSlug) {
            $counter->recordVisit($pageSlug, $pageTitle, [
                'referrer' => $options['referrer']
            ]);
        }
        
        // Get display HTML
        $html = $counter->getVisitorCountDisplay($pageSlug, $options);
        
        // Apply formatting based on format option
        if ($options['format'] === 'compact') {
            $html = formatCompactDisplay($html, $counter, $pageSlug, $options);
        } elseif ($options['format'] === 'numbers-only') {
            $html = formatNumbersOnlyDisplay($counter, $pageSlug, $options);
        }
        
        return $html;
        
    } catch (Exception $e) {
        error_log("Visitor counter display error: " . $e->getMessage());
        return '<!-- Visitor counter error -->';
    }
}

/**
 * Get just the visitor count numbers (for AJAX updates, etc.)
 * 
 * @param string $pageSlug The page identifier
 * @return array Visitor count data
 */
function getVisitorCountData($pageSlug = null) {
    try {
        if (!$pageSlug) {
            $pageSlug = getPageSlugFromRequest();
        }
        
        $counter = new VisitorCounter();
        $pageStats = $counter->getPageStats($pageSlug);
        $siteStats = $counter->getSiteStats();
        
        return [
            'page_slug' => $pageSlug,
            'page_visits' => $pageStats['total_visits'] ?? 0,
            'page_unique_visits' => $pageStats['unique_visits'] ?? 0,
            'page_today_visits' => $pageStats['today_visits'] ?? 0,
            'site_total_visits' => $siteStats['total_site_visits'] ?? 0,
            'site_today_visits' => $siteStats['today_total_visits'] ?? 0,
            'site_unique_visitors' => $siteStats['unique_site_visitors'] ?? 0,
            'last_updated' => date('Y-m-d H:i:s')
        ];
        
    } catch (Exception $e) {
        error_log("Error getting visitor count data: " . $e->getMessage());
        return [];
    }
}

/**
 * Display a simple retro-style counter (like old web counters)
 * 
 * @param string $pageSlug The page identifier
 * @param array $options Display options
 * @return string HTML output
 */
function displayRetroCounter($pageSlug = null, $options = []) {
    $defaultOptions = [
        'digits' => 6,              // Number of digits to display
        'show_label' => true,       // Show "Visitors:" label
        'counter_type' => 'page',   // 'page' or 'site'
        'style' => 'lcd'            // 'lcd', 'led', 'mechanical'
    ];
    
    $options = array_merge($defaultOptions, $options);
    
    try {
        if (!$pageSlug) {
            $pageSlug = getPageSlugFromRequest();
        }
        
        $counter = new VisitorCounter();
        
        if ($options['counter_type'] === 'site') {
            $siteStats = $counter->getSiteStats();
            $count = $siteStats['total_site_visits'] ?? 0;
            $label = 'Total Site Visitors:';
        } else {
            $pageStats = $counter->getPageStats($pageSlug);
            $count = $pageStats['total_visits'] ?? 0;
            $label = 'Page Visitors:';
        }
        
        // Format number with leading zeros
        $formattedCount = str_pad($count, $options['digits'], '0', STR_PAD_LEFT);
        
        $html = '<div class="retro-counter retro-counter-' . $options['style'] . '">';
        
        if ($options['show_label']) {
            $html .= '<div class="counter-label">' . $label . '</div>';
        }
        
        $html .= '<div class="counter-display">';
        
        // Split into individual digits for styling
        for ($i = 0; $i < strlen($formattedCount); $i++) {
            $digit = $formattedCount[$i];
            $html .= '<span class="counter-digit">' . $digit . '</span>';
        }
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
        
    } catch (Exception $e) {
        error_log("Retro counter display error: " . $e->getMessage());
        return '<!-- Retro counter error -->';
    }
}

/**
 * Auto-detect page slug from current request
 * 
 * @return string Page slug
 */
function getPageSlugFromRequest() {
    // Try to get from REQUEST_URI
    $uri = $_SERVER['REQUEST_URI'] ?? '';
    $path = parse_url($uri, PHP_URL_PATH);
    
    // Remove file extension and directory paths
    $slug = basename($path, '.php');
    $slug = basename($slug, '.html');
    
    // Handle index pages
    if ($slug === 'index' || empty($slug)) {
        // Try to get from directory name
        $dir = dirname($path);
        $dirName = basename($dir);
        
        if ($dirName && $dirName !== '.' && $dirName !== 'content') {
            $slug = $dirName . '-index';
        } else {
            $slug = 'home';
        }
    }
    
    // Clean the slug
    $slug = preg_replace('/[^a-z0-9-]/', '-', strtolower($slug));
    $slug = preg_replace('/-+/', '-', $slug);
    $slug = trim($slug, '-');
    
    return $slug ?: 'unknown-page';
}

/**
 * Format compact display
 */
function formatCompactDisplay($html, $counter, $pageSlug, $options) {
    $pageStats = $counter->getPageStats($pageSlug);
    $siteStats = $counter->getSiteStats();
    
    $compactHtml = '<div class="visitor-counter visitor-counter-compact">';
    
    if ($options['show_page_count'] && $pageStats) {
        $compactHtml .= '<span class="page-count">' . number_format($pageStats['total_visits']) . '</span>';
    }
    
    if ($options['show_site_count'] && !empty($siteStats)) {
        if ($options['show_page_count']) {
            $compactHtml .= ' | ';
        }
        $compactHtml .= '<span class="site-count">' . number_format($siteStats['total_site_visits'] ?? 0) . '</span>';
    }
    
    $compactHtml .= '</div>';
    
    return $compactHtml;
}

/**
 * Format numbers-only display
 */
function formatNumbersOnlyDisplay($counter, $pageSlug, $options) {
    $pageStats = $counter->getPageStats($pageSlug);
    $siteStats = $counter->getSiteStats();
    
    $numbers = [];
    
    if ($options['show_page_count'] && $pageStats) {
        $numbers[] = $pageStats['total_visits'];
    }
    
    if ($options['show_site_count'] && !empty($siteStats)) {
        $numbers[] = $siteStats['total_site_visits'] ?? 0;
    }
    
    return implode(',', $numbers);
}

/**
 * Initialize visitor counter (call this once per page load)
 * 
 * @param array $options Global options
 */
function initVisitorCounter($options = []) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Set global options
    $GLOBALS['visitor_counter_options'] = array_merge([
        'auto_track' => true,
        'cache_enabled' => true,
        'debug_mode' => false
    ], $options);
    
    // Auto-track visit if enabled
    if ($GLOBALS['visitor_counter_options']['auto_track']) {
        $pageSlug = getPageSlugFromRequest();
        $pageTitle = $GLOBALS['page_title'] ?? null;
        
        try {
            $counter = new VisitorCounter();
            $counter->recordVisit($pageSlug, $pageTitle);
        } catch (Exception $e) {
            if ($GLOBALS['visitor_counter_options']['debug_mode']) {
                error_log("Auto-track visitor error: " . $e->getMessage());
            }
        }
    }
}

/**
 * Get visitor statistics for admin/analytics purposes
 * 
 * @param array $options Query options
 * @return array Statistics data
 */
function getVisitorAnalytics($options = []) {
    try {
        $counter = new VisitorCounter();
        $siteStats = $counter->getSiteStats();
        
        // Get top pages (you might want to implement this in VisitorCounter class)
        // For now, return basic site stats
        return [
            'site_stats' => $siteStats,
            'generated_at' => date('Y-m-d H:i:s')
        ];
        
    } catch (Exception $e) {
        error_log("Error getting visitor analytics: " . $e->getMessage());
        return [];
    }
}
