# Comments System Setup Guide

This guide will help you set up the comment system with Google OAuth authentication for your Obsidian-Quartz blog.

## Prerequisites

- PHP 7.4 or higher
- MySQL/MariaDB database
- Google Cloud Console account
- Web server (Apache/Nginx)

## Step 1: Database Setup

1. Create a new MySQL database for comments:
```sql
CREATE DATABASE obsidian_quartz_comments;
```

2. Import the database schema:
```bash
mysql -u your_username -p obsidian_quartz_comments < comments/comments.sql
```

## Step 2: Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Set application type to "Web application"
6. Add your authorized redirect URIs:
   - `http://your-domain.com/comments/google-auth.php`
   - `http://localhost/your-site/comments/google-auth.php` (for testing)

## Step 3: Configuration

1. Edit `comments/config.php`:
   - Update database credentials
   - Add your Google OAuth client ID and secret
   - Set your redirect URI
   - Add your Google ID to the admin list

```php
'database' => [
    'host' => 'localhost',
    'dbname' => 'obsidian_quartz_comments',
    'username' => 'your_db_username',
    'password' => 'your_db_password',
],

'google' => [
    'client_id' => 'your_google_client_id.apps.googleusercontent.com',
    'client_secret' => 'your_google_client_secret',
    'redirect_uri' => 'http://your-domain.com/comments/google-auth.php',
],

'admin' => [
    'admin_google_ids' => [
        'your_google_id_here'
    ],
],
```

## Step 4: File Permissions

Make sure the web server can read the comments directory:
```bash
chmod -R 755 comments/
```

## Step 5: Testing

1. Visit any individual post page on your site
2. You should see a "Sign in with Google" button
3. After signing in, you should be able to post comments
4. Comments should appear immediately (unless moderation is enabled)

## Features

### For Users:
- Google OAuth authentication
- Post comments and replies
- Like/dislike comments
- Real-time comment loading

### For Admins:
- Automatic spam detection
- Rate limiting
- Comment moderation (optional)
- User management

## Security Features

- CSRF protection
- SQL injection prevention
- XSS protection
- Rate limiting
- Spam detection with configurable patterns
- Honeypot fields for bot detection

## Customization

### Spam Detection
Edit spam patterns in the database `spam_patterns` table or through the admin interface.

### Rate Limiting
Adjust rate limits in `config.php`:
```php
'rate_limits' => [
    'comments_per_hour' => 10,
    'comments_per_day' => 50,
    'votes_per_hour' => 100,
],
```

### Styling
Comments are styled using CSS classes in `css/style.css`. Key classes:
- `.comments-section`
- `.comment-form-container`
- `.comment`
- `.comment-reply`

## Troubleshooting

### Common Issues:

1. **"Database connection failed"**
   - Check database credentials in `config.php`
   - Ensure MySQL service is running

2. **"Google Auth Error"**
   - Verify Google OAuth credentials
   - Check redirect URI matches exactly
   - Ensure Google+ API is enabled

3. **Comments not appearing**
   - Check if moderation is enabled in config
   - Look for JavaScript errors in browser console
   - Verify file permissions

4. **Spam detection too aggressive**
   - Adjust `spam_threshold` in config
   - Review spam patterns in database

### Debug Mode
Enable debug mode by adding to `config.php`:
```php
'debug' => true,
```

## API Endpoints

The comment system provides these API endpoints:

- `GET comments/comment-handler.php?action=comments&post_slug=slug` - Get comments
- `POST comments/comment-handler.php?action=comment` - Post comment
- `POST comments/comment-handler.php?action=vote` - Vote on comment
- `GET comments/comment-handler.php?action=user` - Get current user

## Database Schema

### Tables:
- `users` - Google OAuth user data
- `comments` - Comment content and metadata
- `comment_votes` - Like/dislike votes
- `spam_patterns` - Spam detection patterns
- `rate_limits` - Rate limiting data

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review browser console for JavaScript errors
3. Check web server error logs
4. Verify database connectivity

## License

This comment system is part of the Obsidian-Quartz blog system.
