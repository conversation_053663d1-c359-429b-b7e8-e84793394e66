-- Simple MariaDB Compatible Visitor Counter Setup

-- Create the main visitor tracking table
CREATE TABLE IF NOT EXISTS aachipsc_blog_page_visits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_slug VARCHAR(255) NOT NULL,
    page_title VARCHAR(500) DEFAULT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent_hash VARCHAR(64) NOT NULL,
    visit_date DATE NOT NULL,
    visit_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_page_slug (page_slug),
    INDEX idx_ip_address (ip_address),
    INDEX idx_visit_date (visit_date),
    UNIQUE KEY unique_daily_visit (page_slug, ip_address, user_agent_hash, visit_date)
);

-- Create the page statistics table
CREATE TABLE IF NOT EXISTS aachipsc_blog_page_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_slug VARCHAR(255) NOT NULL UNIQUE,
    page_title VARCHAR(500) DEFAULT NULL,
    total_visits INT DEFAULT 0,
    unique_visits INT DEFAULT 0,
    today_visits INT DEFAULT 0,
    last_visit TIMESTAMP NULL,
    first_visit TIMESTAMP NULL,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_page_slug (page_slug)
);

-- Create the site-wide statistics table
CREATE TABLE IF NOT EXISTS aachipsc_blog_site_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stat_name VARCHAR(100) NOT NULL UNIQUE,
    stat_value BIGINT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create visitor sessions table
CREATE TABLE IF NOT EXISTS aachipsc_blog_visitor_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    user_agent_hash VARCHAR(64) NOT NULL,
    first_visit TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_visit TIMESTAMP NULL,
    total_page_views INT DEFAULT 1,
    INDEX idx_ip_address (ip_address),
    UNIQUE KEY unique_visitor (ip_address, user_agent_hash)
);

-- Insert initial site statistics
INSERT IGNORE INTO aachipsc_blog_site_stats (stat_name, stat_value) VALUES
('total_site_visits', 0),
('unique_site_visitors', 0),
('site_launch_date', UNIX_TIMESTAMP(NOW()));

-- Insert some test data
INSERT IGNORE INTO aachipsc_blog_page_visits 
(page_slug, page_title, ip_address, user_agent_hash, visit_date) 
VALUES 
('home', 'Home Page', '127.0.0.1', SHA2('test-browser', 256), CURDATE()),
('test-page', 'Test Page', '127.0.0.1', SHA2('test-browser', 256), CURDATE());

-- Update page stats for test data
INSERT IGNORE INTO aachipsc_blog_page_stats 
(page_slug, page_title, total_visits, unique_visits, today_visits, first_visit, last_visit) 
VALUES 
('home', 'Home Page', 1, 1, 1, NOW(), NOW()),
('test-page', 'Test Page', 1, 1, 1, NOW(), NOW());

-- Update site stats
UPDATE aachipsc_blog_site_stats SET stat_value = 2 WHERE stat_name = 'total_site_visits';
UPDATE aachipsc_blog_site_stats SET stat_value = 1 WHERE stat_name = 'unique_site_visitors';
