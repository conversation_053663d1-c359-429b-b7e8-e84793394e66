-- Comments System Database Schema
-- For A. A. Chips' Obsidian-Quartz Blog

CREATE DATABASE IF NOT EXISTS obsidian_quartz_comments;
USE obsidian_quartz_comments;

-- Users table for Google OAuth authentication
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    google_id VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    picture_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_banned BOOLEAN DEFAULT FALSE,
    INDEX idx_google_id (google_id),
    INDEX idx_email (email)
);

-- Comments table
CREATE TABLE IF NOT EXISTS comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    post_slug VARCHAR(255) NOT NULL,
    user_id INT NOT NULL,
    parent_id INT NULL, -- For threaded comments/replies
    content TEXT NOT NULL,
    is_approved BOOLEAN DEFAULT TRUE, -- For moderation
    is_spam BOOLEAN DEFAULT FALSE,
    spam_score DECIMAL(3,2) DEFAULT 0.00, -- 0.00 to 1.00
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    ip_address VARCHAR(45), -- IPv6 compatible
    user_agent TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE,
    INDEX idx_post_slug (post_slug),
    INDEX idx_user_id (user_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_created_at (created_at),
    INDEX idx_approved_spam (is_approved, is_spam)
);

-- Comment votes table (for like/dislike functionality)
CREATE TABLE IF NOT EXISTS comment_votes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    comment_id INT NOT NULL,
    user_id INT NOT NULL,
    vote_type ENUM('like', 'dislike') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (comment_id) REFERENCES comments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_vote (comment_id, user_id),
    INDEX idx_comment_id (comment_id),
    INDEX idx_user_id (user_id)
);

-- Spam detection patterns table
CREATE TABLE IF NOT EXISTS spam_patterns (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pattern VARCHAR(500) NOT NULL,
    pattern_type ENUM('keyword', 'regex', 'url') NOT NULL,
    weight DECIMAL(3,2) DEFAULT 0.50, -- How much this pattern contributes to spam score
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_pattern_type (pattern_type),
    INDEX idx_active (is_active)
);

-- Rate limiting table
CREATE TABLE IF NOT EXISTS rate_limits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    user_id INT NULL,
    action_type ENUM('comment', 'vote', 'login') NOT NULL,
    action_count INT DEFAULT 1,
    window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_ip_action (ip_address, action_type),
    INDEX idx_user_action (user_id, action_type),
    INDEX idx_window_start (window_start)
);

-- Insert some basic spam patterns
INSERT INTO spam_patterns (pattern, pattern_type, weight) VALUES
('viagra', 'keyword', 0.8),
('cialis', 'keyword', 0.8),
('casino', 'keyword', 0.7),
('lottery', 'keyword', 0.7),
('bitcoin', 'keyword', 0.6),
('crypto', 'keyword', 0.5),
('investment', 'keyword', 0.4),
('make money', 'keyword', 0.6),
('click here', 'keyword', 0.5),
('free money', 'keyword', 0.7),
('http[s]?://[^\s]+\.(tk|ml|ga|cf)', 'regex', 0.9),
('[A-Z]{10,}', 'regex', 0.6),
('(.)\1{4,}', 'regex', 0.5);

-- Create admin user (replace with actual admin Google ID)
-- INSERT INTO users (google_id, email, name, picture_url) VALUES 
-- ('your_google_id_here', '<EMAIL>', 'A. A. Chips', 'your_picture_url');
