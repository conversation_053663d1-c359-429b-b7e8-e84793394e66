<?php
// Auto-generated blog post
// Source: hadestown-review.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Hadestown. Teen Edition. My Review.';
$meta_description = 'Someone I know was performing in Hadestown. Teen Edition. I got gifted tickets. I went to see it. I am not a theater or musical person, but I listen to musical soundtracks and knew every other word. There was not a dry eye that night.';
$meta_keywords = 'journal, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../img/self/hadestown.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Hadestown. Teen Edition. My Review.',
  'date' => '2025-05-20',
  'author' => 'A. A. Chips',
  'tags' => 
  array (
    0 => 'journal',
  ),
  'excerpt' => 'Someone I know was performing in Hadestown. Teen Edition. I got gifted tickets. I went to see it. I am not a theater or musical person, but I listen to musical soundtracks and knew every other word. There was not a dry eye that night.',
  'thumbnail' => '../../img/self/hadestown.jpg',
  'source_file' => 'content\\hadestown-review.md',
);

// Raw content
$post_content = '<p>Someone I know was performing in Hadestown. Teen Edition. I got gifted tickets. I went to see it. I am not a theater or musical person, but I listen to musical soundtracks and knew every other word. There was not a dry eye that night.</p>

<p>Hadestown takes place in a post-apocalyptic world, where the river Styx has dried up, and the gates of the Underworld are crumbling. The story follows Orpheus, a troubadour, and Eurydice, a young woman, as they navigate the treacherous journey to the Underworld to rescue Eurydice from Hades, the god of the Underworld.</p>



<p><figure></p>
<img src="../../img/self/hadestown.jpg" width="400" alt="Selling the flower table for Hadestown.">
<p><figcaption>Selling flowers for Hadestown. We sold out during the intermission.</figcaption></p>
<p></figure></p>


<p>Two lovers, Orpheus and Eurydice, are separated by death. Orpheus embarks on a perilous journey to the Underworld to bring Eurydice back to the world of the living. However, Hades, the god of the Underworld, has other plans. He falls in love with Eurydice and kidnaps her, leading to a tragic confrontation between Orpheus and Hades.</p>

<p>Check out the Tiny Desk Concert done by the original cast of Hadestown.</p>

<iframe width="560" height="315" src="https://www.youtube.com/embed/XKwDFDDr_VA?si=nAMoefXW4c0q4dPR" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

<p>Here\'s my favorite song from the show.</p>

<iframe width="560" height="315" src="https://www.youtube.com/embed/MUQSEXyQsw4?si=NEpM-mSyW0Q_8mOI" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

<p>Do you get it? I am Chips. I make Chips.</p>

<p>I also really like Wait for Me.</p>

<iframe width="560" height="315" src="https://www.youtube.com/embed/ww6_FO8QKt0?si=YBgWMoRPvc0TlYtI" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>