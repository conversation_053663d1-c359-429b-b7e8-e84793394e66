/**
 * Visitor Counter Styles
 * For A. A. Chips' Obsidian-Quartz Blog
 * 
 * Retro-style visitor counter CSS reminiscent of classic website counters
 * from the 1990s and early 2000s.
 */

/* Base visitor counter styles */
.visitor-counter {
    font-family: 'Courier New', monospace;
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    border: 2px solid #444;
    border-radius: 8px;
    padding: 12px 16px;
    margin: 16px 0;
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 2px 4px rgba(0, 0, 0, 0.3),
        0 0 10px rgba(0, 255, 0, 0.1);
    text-align: center;
    color: #00ff00;
    text-shadow: 0 0 5px #00ff00;
    position: relative;
    overflow: hidden;
}

.visitor-counter::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Counter labels */
.visitor-counter .counter-label {
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #00cccc;
    margin-bottom: 4px;
    text-shadow: 0 0 3px #00cccc;
}

/* Counter numbers */
.visitor-counter .counter-number {
    font-size: 16px;
    font-weight: bold;
    color: #00ff00;
    text-shadow: 0 0 8px #00ff00;
    letter-spacing: 1px;
    display: inline-block;
    min-width: 60px;
    background: rgba(0, 0, 0, 0.3);
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #333;
}

/* Page and site counter sections */
.visitor-counter .page-counter,
.visitor-counter .site-counter {
    margin: 4px 0;
    padding: 4px 0;
}

.visitor-counter .site-counter {
    border-top: 1px solid #333;
    padding-top: 8px;
    margin-top: 8px;
}

/* Today count styling */
.visitor-counter .today-count {
    font-size: 10px;
    color: #ffff00;
    text-shadow: 0 0 3px #ffff00;
    margin-left: 8px;
    font-style: italic;
}

/* Retro LCD-style counter */
.retro-counter {
    font-family: 'Courier New', monospace;
    background: #000;
    border: 3px solid #333;
    border-radius: 6px;
    padding: 8px 12px;
    margin: 12px 0;
    text-align: center;
    position: relative;
    box-shadow: 
        inset 0 0 10px rgba(0, 255, 0, 0.3),
        0 2px 6px rgba(0, 0, 0, 0.5);
}

.retro-counter .counter-label {
    font-size: 10px;
    color: #00ff00;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 6px;
    text-shadow: 0 0 5px #00ff00;
}

.retro-counter .counter-display {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2px;
}

.retro-counter .counter-digit {
    display: inline-block;
    width: 20px;
    height: 28px;
    line-height: 28px;
    background: #001100;
    border: 1px solid #003300;
    border-radius: 3px;
    color: #00ff00;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    text-shadow: 0 0 8px #00ff00;
    box-shadow: inset 0 0 5px rgba(0, 255, 0, 0.2);
    position: relative;
}

.retro-counter .counter-digit::before {
    content: '8';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    color: #002200;
    z-index: 1;
}

.retro-counter .counter-digit::after {
    content: attr(data-digit);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    color: #00ff00;
    z-index: 2;
}

/* LED style variant */
.retro-counter-led .counter-digit {
    background: #220000;
    border-color: #440000;
    color: #ff0000;
    text-shadow: 0 0 8px #ff0000;
    box-shadow: inset 0 0 5px rgba(255, 0, 0, 0.2);
}

.retro-counter-led .counter-digit::before {
    color: #440000;
}

.retro-counter-led .counter-digit::after {
    color: #ff0000;
}

.retro-counter-led .counter-label {
    color: #ff0000;
    text-shadow: 0 0 5px #ff0000;
}

/* Mechanical style variant */
.retro-counter-mechanical {
    background: linear-gradient(135deg, #444 0%, #222 100%);
    border-color: #666;
}

.retro-counter-mechanical .counter-digit {
    background: linear-gradient(135deg, #f0f0f0 0%, #d0d0d0 100%);
    border: 2px solid #999;
    color: #000;
    text-shadow: none;
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.5),
        0 1px 2px rgba(0, 0, 0, 0.3);
}

.retro-counter-mechanical .counter-digit::before {
    display: none;
}

.retro-counter-mechanical .counter-digit::after {
    color: #000;
}

.retro-counter-mechanical .counter-label {
    color: #ccc;
    text-shadow: 0 1px 0 #000;
}

/* Compact visitor counter */
.visitor-counter-compact {
    display: inline-block;
    background: #000;
    border: 1px solid #333;
    border-radius: 4px;
    padding: 4px 8px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #00ff00;
    text-shadow: 0 0 3px #00ff00;
    margin: 0 4px;
}

.visitor-counter-compact .page-count,
.visitor-counter-compact .site-count {
    font-weight: bold;
    letter-spacing: 1px;
}

/* Modern style variant */
.visitor-counter-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.visitor-counter-modern .counter-label {
    color: rgba(255, 255, 255, 0.9);
    text-shadow: none;
}

.visitor-counter-modern .counter-number {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.visitor-counter-modern .today-count {
    color: #ffeb3b;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Minimal style variant */
.visitor-counter-minimal {
    background: transparent;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #666;
    text-shadow: none;
    box-shadow: none;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.visitor-counter-minimal .counter-label {
    color: #999;
    text-shadow: none;
}

.visitor-counter-minimal .counter-number {
    background: #f5f5f5;
    border: 1px solid #ddd;
    color: #333;
    text-shadow: none;
}

.visitor-counter-minimal .today-count {
    color: #007cba;
    text-shadow: none;
}

/* Responsive design */
@media (max-width: 768px) {
    .visitor-counter {
        padding: 8px 12px;
        margin: 12px 0;
    }
    
    .visitor-counter .counter-number {
        font-size: 14px;
        min-width: 50px;
    }
    
    .retro-counter .counter-digit {
        width: 16px;
        height: 24px;
        line-height: 24px;
        font-size: 14px;
    }
    
    .visitor-counter-compact {
        font-size: 11px;
        padding: 3px 6px;
    }
}

@media (max-width: 480px) {
    .visitor-counter {
        padding: 6px 8px;
        margin: 8px 0;
    }
    
    .visitor-counter .counter-label {
        font-size: 10px;
    }
    
    .visitor-counter .counter-number {
        font-size: 12px;
        min-width: 40px;
    }
    
    .retro-counter .counter-digit {
        width: 14px;
        height: 20px;
        line-height: 20px;
        font-size: 12px;
    }
}

/* Animation for number changes */
.visitor-counter .counter-number {
    transition: all 0.3s ease;
}

.visitor-counter .counter-number.updated {
    animation: numberPulse 0.6s ease;
}

@keyframes numberPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); text-shadow: 0 0 15px currentColor; }
    100% { transform: scale(1); }
}

/* Accessibility improvements */
.visitor-counter:focus-within {
    outline: 2px solid #00ff00;
    outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
    .visitor-counter::before,
    .visitor-counter .counter-number.updated {
        animation: none;
    }
}

/* Print styles */
@media print {
    .visitor-counter {
        background: white !important;
        color: black !important;
        border: 1px solid black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    .visitor-counter .counter-label,
    .visitor-counter .counter-number,
    .visitor-counter .today-count {
        color: black !important;
        text-shadow: none !important;
    }
}
