<?php
// Auto-generated blog post
// Source: db-bros-save-day.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'The Diarrhea Brothers Save the Day';
$meta_description = '<PERSON>, <PERSON> and <PERSON> are delivery drivers for their family business. <PERSON>, a new hire, gets wrapped up in their world of chaos and diarrhea. A film by <PERSON>';
$meta_keywords = 'humor, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://i.ytimg.com/vi/h3UA8yfKRFY/maxresdefault.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'The Diarrhea Brothers Save the Day',
  'author' => 'Joel Haver',
  'excerpt' => 'John, Jonathan and Leonard Diarrhea are delivery drivers for their family business. Winston, a new hire, gets wrapped up in their world of chaos and diarrhea. A film by Joel Haver',
  'tags' => 
  array (
    0 => 'humor',
  ),
  'thumbnail' => 'https://i.ytimg.com/vi/h3UA8yfKRFY/maxresdefault.jpg',
  'source_file' => 'content\\humor\\db-bros-save-day.md',
);

// Raw content
$post_content = '<p>This is by far the best stupidest film I have ever watched.</p>
<iframe width="560" height="315" src="https://www.youtube.com/embed/h3UA8yfKRFY?si=rmyNnaQmxwRxOmqm" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

<h1>The Diarrhea Brothers Save The Day</h1>

<p>Joel Haver</p>

<p>2.06M subscribers</p>
<p>120,914 views Nov 23, 2024</p>

<p>John, Jonathan, and Leonard Diarrhea are delivery drivers for their family business. Winston, a new hire, gets wrapped up in their world of chaos. A film by Joel Haver, featuring Trent Lenkarski, Alex Martens, Mason Carter, Sethward, Paulette Jones, Lars Midthun, Brittney Rae, Paulina Gregory, Syd Smith, Benny Ball, Ryan The Leader, Magenta Squash, Yehslacks, Sven Johnson, Gus Toonz, Daxflame, Tom Goulet, Firas Catler, Sharhar Hillel, Walt Lusk, StaggerLee Cole. Original Score by Trent Zulkiewicz and Droodle. Additional Music by Kevin, Cam Raleigh, and MyKey The Artist. Special Effects by Tom Goulet. Special Effects Makeup by Jenn Osborne and Leigh Mader.</p>

<p>Other Channels:</p>
<ul><li>Joel Talks About Movies</li>
<p><li>goodlongpee</li></p>

<p>Support:</p>
<p><li>Patreon: Joel Haver</li></p>
<p><li>Paypal: bit.ly/2ZI7uff</li></p>

<p>Merch: joelstuff.store</p>

<p>Social:</p>
<p><li>Instagram: joelhaver</li></p>
<p><li>Twitter: joelhaver</li></p>
<p><li>Drawings: joeldrawsandwriteshaver</li></p>
<p><li>Letterboxd: joelhaver</li></ul></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>