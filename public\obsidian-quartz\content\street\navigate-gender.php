<?php
// Auto-generated blog post
// Source: navigate-gender.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'A Quick Guide to Navigating Gender';
$meta_description = 'A Quick Guide to Navigating Gender';
$meta_keywords = 'homeless, advocacy, alienation, accessibility, a11y, april, CompassionateCities, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://m.media-amazon.com/images/I/61-EywnqF3L._AC_SL1500_.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'A Quick Guide to Navigating Gender',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'tags' => 
  array (
    0 => 'homeless',
    1 => 'advocacy',
    2 => 'alienation',
    3 => 'accessibility',
    4 => 'a11y',
    5 => 'april',
    6 => 'CompassionateCities',
  ),
  'excerpt' => 'A Quick Guide to Navigating Gender',
  'categories' => 
  array (
    0 => 'Street Advocacy',
    1 => 'Alienation',
    2 => 'Writings',
  ),
  'thumbnail' => 'https://m.media-amazon.com/images/I/61-EywnqF3L._AC_SL1500_.jpg',
  'source_file' => 'content\\street\\navigate-gender.md',
);

// Raw content
$post_content = '<p>What\'s the difference between gender and sexuality? Gender is all about who you are inside, your core identity. Sexuality is about who you\'re attracted to.</p>

<p>How many genders are there? It\'s not just male and female. Culture plays a huge role! Some societies recognize many genders, while others stick to a binary. Transgender is an umbrella term for many identities, including Non-Binary, Genderfluid, and more. Think of it like a diverse spectrum.</p>

<p>Is being transgender a mental illness? Nope! But gender dysphoria, when your body doesn\'t align with your gender identity, can cause real stress and sadness. It\'s about feeling mismatched.</p>

<p>How does being transgender impact daily life? In countless ways. Transgender people often face discrimination, violence, and barriers to healthcare, employment, and even using public restrooms safely. Imagine just trying to use the bathroom and facing harassment. These challenges make life a lot tougher.</p>

<p>Do all trans people want surgery? Definitely not! Transitioning is a personal journey. Some opt for surgery or hormones, while others don\'t. It\'s about what makes each person feel most comfortable in their own skin.</p>

<p>Why is gender important? It\'s about respecting who people are. Imagine if everyone kept guessing your religion or job wrong. That\'s how it feels when gender is mismatched. It\'s okay to ask someone\'s pronouns. If unsure, use "they."</p>

<p>Finally, let\'s be kind. Calling someone "it" or asking about their genitals is rude. Just treat others with respect. Gender is personal, and understanding starts with listening and empathy.</p>

<p>Transgender is not an ideology. It’s not an agenda. Nor is it a fad, a lurking terror, nor a sexuality. Transgender is a variety of people that make up 2.5 million Americans. That’s only the ones who are out on census data. We are a people that have existed and been routinely erased for mthousands of years, in every single culture. We’re also reverred in preclass cultures where resources are distributed equitably. We are in more recent times targets for harassment and violence and discrimination. It hasn’t always been this way, We;ve led peasant re belliohnsand have destroyed statehoodsof power. If you erase us, we will still be here and we will not go down without a fight.</p>

<img src="https://m.media-amazon.com/images/I/61-EywnqF3L._AC_SL1500_.jpg" alt="whatever just wash your hands." width="400">';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>