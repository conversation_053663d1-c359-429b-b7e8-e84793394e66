<?php
// Auto-generated blog post
// Source: help-during-panic-attack.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Ways to be helpful during a loved one\'s panic attack';
$meta_description = 'Talk to me to help keep me grounded. Once I\'m lost in my thoughts it\'s harder to get out of the panic attack. If possible, assist me to a safe spot. Don\'t undermine my panic. Don\'t yell at me. Acknowledge it. Understand. Have empathy.';
$meta_keywords = 'advocacy, a11y, addiction, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../img/gazarubble.webp';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Ways to be helpful during a loved one\'s panic attack',
  'author' => 'Mental Health on the Mighty',
  'excerpt' => 'Talk to me to help keep me grounded. Once I\'m lost in my thoughts it\'s harder to get out of the panic attack. If possible, assist me to a safe spot. Don\'t undermine my panic. Don\'t yell at me. Acknowledge it. Understand. Have empathy.',
  'tags' => 
  array (
    0 => 'advocacy',
    1 => 'a11y',
    2 => 'addiction',
  ),
  'thumbnail' => '../../img/gazarubble.webp',
  'source_file' => 'content\\inspiration\\help-during-panic-attack.md',
);

// Raw content
$post_content = '<h3>Ways to be helpful during a loved one\'s panic attack</h3>

<ul><li>Talk to me to help keep me grounded. Once I\'m lost in my thoughts it\'s harder to get out of the panic attack.</li>

<p><li>If possible, assist me to a safe spot.</li></p>

<p><li>Don\'t undermine my panic. Don\'t yell at me. Acknowledge it. Understand. Have empathy.</li></p>

<p><li>Don\'t ignore me because it\'s uncomfortable – remind me I\'m safe and that I\'m going to be ok.</li></p>

<p><li>Don\'t bring more attention to me! The more people who see the panic in my eyes, the more I panic. I just need space, room to breathe and understanding.</li></p>

<p><li>Distract me, talk about beautiful things.</li></p>

<p><li>Help me focus elsewhere. Show me things aren\'t deadly or threatening. Distract me without judgment.</li></p>

<p><li>Ask me if there\'s anything I need or anything you can do to help. If I say no, tell me you\'re there, I need you and that everything is going to be ok.</li></p>

<p><li>Stay by my side, don\'t make it a big deal. Breathe with me.</li></p>

<p><li>During a panic attack, ask if it\'s ok if you come close. Getting in my face can make the attack worse. Sometimes holding my hand helps, sometimes it\'s a trigger.</li></p>

<p><li>Never tell the person having the attack to \'get over it.\'</li></p>

<p><li>Remind me this won\'t last forever.</li></p>

<p><li>Keep yourself calm. I will eventually feed off your calmness and I\'ll be able to calm down.</li></p>

<p><li>Sometimes I don\'t need someone to try and fix it. Sometimes they just need to be there with a simple hug or to hold a hand.</li></ul></p>

<p>From Mental Health on the Mighty</p>

<img src="../../img/gazarubble.webp" width="400" alt="rubble of destruction in gaza strip.">
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>