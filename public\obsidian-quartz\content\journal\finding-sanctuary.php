<?php
// Auto-generated blog post
// Source: finding-sanctuary.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Finding Sanctuary and Solidarity - July 2017';
$meta_description = 'My days in Asheville begin with the gentle awakening that comes from sleeping in my vehicle, my humble bed nestled in the quiet corners of church parking lots. The crisp mountain air, combined with the simplicity of my foam pad and sleeping bags, offers a surprising balm to my sinuses and a welcome reprieve for my back. After a period of quiet reflection, a morning stroll, or a few kind words exchanged with early risers, I make my way to one of the local free meals.';
$meta_keywords = 'journal, homeless, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../img/humor/courage.png';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Finding Sanctuary and Solidarity - July 2017',
  'date' => '2017-07-11',
  'author' => 'A. A. Chips',
  'tags' => 
  array (
    0 => 'journal',
    1 => 'homeless',
  ),
  'excerpt' => 'My days in Asheville begin with the gentle awakening that comes from sleeping in my vehicle, my humble bed nestled in the quiet corners of church parking lots. The crisp mountain air, combined with the simplicity of my foam pad and sleeping bags, offers a surprising balm to my sinuses and a welcome reprieve for my back. After a period of quiet reflection, a morning stroll, or a few kind words exchanged with early risers, I make my way to one of the local free meals.',
  'thumbnail' => '../../img/humor/courage.png',
  'source_file' => 'content\\journal\\finding-sanctuary.md',
);

// Raw content
$post_content = '<p>My days in Asheville begin with the gentle awakening that comes from sleeping in my vehicle, my humble bed nestled in the quiet corners of church parking lots. The crisp mountain air, combined with the simplicity of my foam pad and sleeping bags, offers a surprising balm to my sinuses and a welcome reprieve for my back. After a period of quiet reflection, a morning stroll, or a few kind words exchanged with early risers, I make my way to one of the local free meals.</p>

<p>If I have the energy and there’s a need, I’m happy to lend a hand. I’ve found a particular appreciation for Haywood St Congregation’s philosophy, where those assisting are treated as companions, equals sharing a moment, rather than simply as volunteers fulfilling a duty.</p>

<p>It’s here, in these shared meals, that the concept of solidarity over charity truly resonates. While the practical assistance of volunteers is invaluable, I believe we must look beyond the act of giving. We need to bridge the chasm we perceive between the ‘haves’ and ‘have-nots,’ to dismantle the misconception that these resources are solely for the most destitute. If you have the time and the capacity, I urge you to attend a free meal, not just to offer help, but to connect, to share, and to contribute in whatever way you can. These gatherings hold a transformative power. When we break bread together, we dismantle the barriers that separate us, fostering a sense of peace and genuine equity. Every person present, myself included, carries a worth far beyond their current circumstances.</p>

<p>My journey to this unconventional lifestyle was a winding path, a blend of choice and necessity. Armed with a Bachelor\'s in Psychology, my college years ironically ushered in a cascade of health issues that defied easy diagnosis. Environmental sensitivities, stress manifesting as debilitating back and neck pain, and a host of immune and thyroid problems became my unwelcome companions. In the midst of this, I discovered that meaningful service became a potent form of medicine, alongside the restorative power of fresh air, ample sleep, long walks, and engagement in grassroots movements.</p>

<p>Living in my family’s home state of Maryland eventually became detrimental to my well-being, reaching a critical point during the last election cycle. The charged atmosphere near DC surrounding the recent Inauguration became unbearable. I made the difficult decision to pack my car and leave. While I had brushed against the edges of homelessness before, the lack of a meaningful way to contribute to something larger only deepened my suffering.</p>

<p>Asheville called to me with the promise of a welcoming community for vehicle dwellers. The pull of the mountains, the allure of fresh spring water, and the tranquility of the rainforest were strong. There was also a faint sense of historical safety here, a feeling that others like me had perhaps sought refuge in these hills before. I was drawn to the progressive initiatives supporting vulnerable populations, particularly those involved in rescuing food. I feel a sense of belonging, as if I’ve been adopted into a community undergoing rapid change, and I am driven by a desire to give back in gratitude.</p>

<p>This is just a small glimpse into my story, but I find immeasurable value in the stories of others I encounter at these shared meals. Their experiences weave a rich tapestry that enriches my own life, leaving me feeling incredibly wealthy in ways that have nothing to do with material possessions. The simple act of earning or accepting twenty dollars can sometimes feel like a profound struggle. In listening to others, and in sharing these simple meals, I find a sanctuary and a profound sense of solidarity.</p>

<img src="../../img/humor/courage.png" alt="courage is just the absense of the bravery to run away - magnus carlson" width="300">

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>