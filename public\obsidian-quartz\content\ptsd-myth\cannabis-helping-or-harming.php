<?php
// Auto-generated blog post
// Source: cannabis-helping-or-harming.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Is Cannabis Helping or Harming You? A Guide for Traumatic Stress';
$meta_description = 'Challenging conventional wisdom about PTSD and mental health through research, advocacy, and lived experience. A collection of writings on mental health reform.';
$meta_keywords = 'mental health, ptsd, psychology, advocacy, research, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://www.banyantreatmentcenter.com/wp-content/uploads/2022/09/ptsd-history.png';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Is Cannabis Helping or Harming You? A Guide for Traumatic Stress',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'tags' => 
  array (
    0 => 'mental health',
    1 => 'ptsd',
    2 => 'psychology',
    3 => 'advocacy',
    4 => 'research',
  ),
  'excerpt' => 'Challenging conventional wisdom about PTSD and mental health through research, advocacy, and lived experience. A collection of writings on mental health reform.',
  'categories' => 
  array (
    0 => 'PTSD Mythology',
    1 => 'Street Advocacy',
    2 => 'Writings',
  ),
  'thumbnail' => 'https://www.banyantreatmentcenter.com/wp-content/uploads/2022/09/ptsd-history.png',
  'source_file' => 'content\\ptsd-myth\\cannabis-helping-or-harming.md',
);

// Raw content
$post_content = '<p>Cannabis can sometimes offer relief from traumatic stress symptoms. However, it can also lead to unhealthy patterns and potential dependence. Taking breaks to re-evaluate your usage is crucial. Cannabis compounds can remain in your system for weeks, even over a month. Regular, frequent use can lead to a buildup, resulting in various challenges such as lethargy, fatigue, sleep issues, brain fog, executive dysfunction, mood swings, psychosis, and dissociation. Tolerance can also diminish its benefits over time.</p>

<p>To determine whether cannabis is helpful or harmful for you, consider these reflective questions:</p>

<p>Assessing Your Cannabis Use:</p>

<ul><li>Does cannabis consistently alleviate pain and stress?  </li>
<p><li>Has cannabis use prevented the need for more invasive or risky treatments?   </li></p>
<p><li>How does your cannabis use impact your daily functioning?</li></p>
<p><li>What is your frequency and intensity of use? Weekly, daily, multiple times a day? Small doses or until you experience specific effects?</li></p>
<p><li>If you continue your current regimen, do you foresee your condition improving or worsening a year from now?</li></p>
<p><li>Does self-medicating with cannabis lead you to engage in high-risk activities such as reckless driving, neglecting responsibilities, or making poor decisions? </li></p>
<p><li>Are there negative effects stemming from your cannabis use? Is it harming you or your loved ones?</li></p>
<p><li>Considering both positive and negative outcomes, do you believe self-medicating with cannabis is a good choice for your traumatic stress? Would taking a break be beneficial?</li></ul></p>

<p>By honestly answering these questions, you can gain valuable insight into the role cannabis plays in your life and whether adjustments are needed for your well-being.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>