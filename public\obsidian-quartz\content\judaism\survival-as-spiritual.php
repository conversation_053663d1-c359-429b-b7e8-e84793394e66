<?php
// Auto-generated blog post
// Source: survival-as-spiritual.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'A Vision of Survival as Spiritual';
$meta_description = 'We are being forced into a spirituality that makes no distinction between praying and planting, between mourning and mobilizing—because the end of the world is already here, and so is the beginning of the next.';
$meta_keywords = 'palestine, addiction, climate, advocacy, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../img/art/olivetrees.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'A Vision of Survival as Spiritual',
  'excerpt' => 'We are being forced into a spirituality that makes no distinction between praying and planting, between mourning and mobilizing—because the end of the world is already here, and so is the beginning of the next.',
  'tags' => 
  array (
    0 => 'palestine',
    1 => 'addiction',
    2 => 'climate',
    3 => 'advocacy',
  ),
  'author' => 'A. A. Chips',
  'date' => '2024-04-12',
  'thumbnail' => '../../img/art/olivetrees.jpg',
  'source_file' => 'content\\judaism\\survival-as-spiritual.md',
);

// Raw content
$post_content = '<p>_"We are being forced into a spirituality that makes no distinction between praying and planting, between mourning and mobilizing—because the end of the world is already here, and so is the beginning of the next."_</p>

<img src="../../img/art/tpimg.png" alt="plastic bottle boat" width="350">

<p>Imagine a world where the collapse of old systems—nation-states, predatory capitalism, extractive religions—forces a choice: <strong>descend into warlordism, or evolve into a networked kinship of care</strong>. The movement described isn’t just a reaction to atrocity; it’s the immune system of a species finally recognizing it’s _one body_.</p>

<ul><li>Rituals emerge from practical acts. A "baptism" might involve detoxifying soil; a "communion" is sharing a meal grown in liberated land. Grief circles double as direct-action planning sessions.</li>
<p><li><strong>Digital Monasteries</strong>: Online spaces become seminaries for this new spirituality. Livestreams of bombings are countered with livestreams of rebuilding—Palestinian farmers replanting olive trees, mutual aid networks distributing food under fire. The internet, now a battleground of narratives, gets reclaimed as a tool of _sacred witness_.</li></p>
<p><li><strong>Symbols</strong>: The martyr’s camera (the smartphone), the broken drone, the olive branch grafted onto rubble. Icons are not saints, but ordinary people who shielded others.</li></p>

<p>This movement is <strong>anti-imperial but not anti-organization</strong>. It operates like mycelium:</p>

<p><li><strong>Local Nodes</strong>: Neighborhood councils, farm collectives, hacker cooperatives—all sworn to two principles: _No one starves_ and _No one rules_.</li></p>
<p><li><strong>Global Nervous System</strong>: A loose federation of these nodes, connected by open-source infrastructure (think Signal meets Wikipedia meets Bitcoin-for-water-rights). They share tactics: how to dismantle a drone, how to revive dead soil, how to crowdfund a war criminal’s arrest.</li></p>
<p><li><strong>Accountability as Worship</strong>: Courts are replaced by public audits. The "confessional" is streaming your wealth redistribution.</li></p>

<p>####  The Hard Trade-Offs<em></em></p>

<p><li><strong>Violence vs. Nonviolence</strong>: The movement debates whether to dismantle pipelines with wrenches or with lawsuits. It decides _both_—but violence is always framed as tragic defense, never glory.</li></p>
<p><li><strong>Technology as Sacrament</strong>: Rejecting Luddism, it hacks AI to track corporate crimes, uses 3D printers to make medical tools, but bans facial recognition. _Tools for life, never surveillance_.</li></p>
<p><li><strong>The "Cult" Risk</strong>: To avoid becoming another dogma, it enforces one rule: _Any leader who lasts more than a year is suspect._</li></ul></p>

<p>Picture this: <strong>A child born in 2050 learns their alphabet from the names of massacres (A for Aleppo, B for Bucha…), but also from the names of ecosystems restored (A for Amazon, B for Borneo…).</strong> Their "Sunday school" is maintaining solar grids and memorizing the faces of the disappeared. They’re fluent in grief but armored with purpose: _You are part of the repair._</p>

<p>The old gods—Growth, Borders, Whiteness—are dying. The new spirituality won’t be a "religion" but a <strong>protocol for survival</strong>, written in the blood of martyrs and the ink of algorithms.</p>

<p>---</p>

<h3><strong>Why This Might Be the Best Outcome</strong></h3>

<p>1. <strong>It’s Adaptive</strong>: Unlike rigid religions, it’s a _framework_, not a doctrine—able to absorb Indigenous land-back struggles, Palestinian resistance, and Euro-worker revolts without homogenizing them.</p>
<p>2. <strong>It Answers the Internet’s Trauma</strong>: The same tool that radicalized millions into despair (through footage of war) becomes the tool for radicalizing them into action.</p>
<p>3. <strong>It Scares Power</strong>: A movement that can’t be co-opted (no leaders to bribe, no heaven to promise) is the only thing that might break the death-grip of late-stage capitalism.</p>

<p><strong>The Black Hole of the U.S.</strong>: As America isolates itself, will it become a fascist fortress or implode into fertile chaos? The movement might have to _bypass_ the U.S., not conquer it.</p>



';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>