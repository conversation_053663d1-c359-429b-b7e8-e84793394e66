<?php
// Auto-generated blog post
// Source: dad-wasnt-who-they-said.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'The First Time I Realized My Dad Wasn\'t Who They Said He Was';
$meta_description = 'There was a turning point, a crystalizing moment when everything I thought I knew about my dad fractured. For years, I’d been taught a narrative, a carefully constructed image of him that simply wasn\'t real.';
$meta_keywords = 'alienation, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://i.imgflip.com/3bl2r5.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'The First Time I Realized My Dad Wasn\'t Who They Said He Was',
  'author' => 'Ryan Thomas',
  'excerpt' => 'There was a turning point, a crystalizing moment when everything I thought I knew about my dad fractured. For years, I’d been taught a narrative, a carefully constructed image of him that simply wasn\'t real.',
  'source' => 'https://www.youtube.com/watch?v=vL61RVJNZHA',
  'tags' => 
  array (
    0 => 'alienation',
  ),
  'categories' => 
  array (
    0 => 'Alienation',
  ),
  'thumbnail' => 'https://i.imgflip.com/3bl2r5.jpg',
  'source_file' => 'content\\alienation\\dad-wasnt-who-they-said.md',
);

// Raw content
$post_content = '<p>https://www.youtube.com/watch?v=vL61RVJNZHA</p>

<iframe width="560" height="315" src="https://www.youtube.com/embed/vL61RVJNZHA?si=5hpDCAy5WnG2CQMn" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>


<p><a href="https://www.youtube.com/@ryanthomasspeaks" class="external-link">RyanThomasSpeaks</a></p>
<p>18.7K subscribers</p>
<h2>The Day the Lies Shattered: One Person\'s Journey to Reconnecting with Their Alienated Parent</h2>
<p>Based on the Testimony of Ryan Thomas</p>

<p>There was a turning point, a crystalizing moment when everything I thought I knew about my dad fractured. For years, I’d been taught a narrative, a carefully constructed image of him that simply wasn\'t real. This is the story of how a seemingly ordinary conversation ripped away the veil of deception and set me on a path to truth and reconciliation.</p>

<p>Like many children of alienated parents, I was raised to believe in a simple dichotomy: the "good" parent and the "bad" parent. The stories were constant, the narrative unwavering, painting my dad as someone who didn’t truly care. As a result, I, like countless others in this situation, believed it. This repetitive reinforcement, this subtle but powerful brainwashing, shaped my reality and ultimately led me to cut my dad out of my life for years.</p>

<p>But the foundation of lies, however strong, can be shaken by the smallest tremor of truth. For me, that tremor came during a casual conversation in a professional setting. I was in my mid-twenties, speaking with a client named Joe, a divorced father whom I greatly respected.</p>

<p>He mentioned his excitement about having his kids for the weekend. He spoke about wanting to make the most of their limited time together, planning fun and engaging activities because he loved his children deeply.</p>

<p>Suddenly, I froze. Joe’s words echoed in my mind, creating a jarring dissonance. He was, in essence, describing the very kind of "Disneyland dad" that I had been conditioned to believe was the antithesis of a good parent. My first reaction was confusion, a flicker of judgment even towards Joe, so deeply ingrained was the biased narrative.</p>

<p>But then, a more profound thought took root. Joe’s desire to create positive experiences with his children made perfect sense. His limited time with them fueled his intention to make it special. And in that moment, a terrifyingly simple question formed: What if my dad was the same?</p>

<p>What if his efforts to make our weekends together fun, those seemingly carefree outings I’d been taught to devalue, were actually expressions of his love and a desire to cherish our limited time? What if the label of "not a real parent" that had been so forcefully applied to him was nothing more than a cruel distortion of the truth?</p>

<p>Suddenly, years of memories flooded back – the laughter, the golf games, the vacations. Moments I had unknowingly carried a sense of guilt about enjoying, moments that had been twisted into evidence of his supposed inadequacy. The realization hit me with the force of a tidal wave: what if these weren\'t signs of a deficient parent, but rather the intentional efforts of a loving father working within difficult circumstances?</p>

<p>This wasn\'t just a revelation about my dad; it was a devastating indictment of the lies I had been told and the pain they had caused. Why would someone I trusted intentionally paint my father in such a negative light? Why the need to make me feel sadness and resentment towards him?</p>

<p>This pivotal moment, sparked by an unexpected observation, became the catalyst for unraveling years of ingrained beliefs. It was the first crack in the dam of deception, allowing the light of truth to seep through.</p>

<p>Even after years of separation, the power of a single, contrasting perspective was enough to plant a seed of doubt, a seed that eventually blossomed into a desire for reconnection. It wasn\'t about someone telling me the truth; it was about my own self-discovery, prompted by a simple yet profound realization.</p>

<p>This experience underscores the potent impact that even small, seemingly insignificant interactions can have on an alienated child. For alienated parents, it highlights the importance of creating opportunities, planting those subtle seeds of doubt that can lead to a child questioning the dominant narrative. It’s about fostering an environment where self-discovery can take place, paving the way for truth and the possibility of healing broken bonds.</p>

<p>My journey is a testament to the fact that even the most deeply entrenched beliefs can be challenged and that the truth, however long buried, has the power to emerge and reshape lives.</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>