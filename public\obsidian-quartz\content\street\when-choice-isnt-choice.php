<?php
// Auto-generated blog post
// Source: when-choice-isnt-choice.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'When "Choice" Isn’t Really a Choice';
$meta_description = 'Addressing the misconception that homelessness is a choice and exploring the reality of limited options';
$meta_keywords = 'homelessness, advocacy, misconceptions, personal, draft, videoscript, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../img/siblingTruth.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'When "Choice" Isn’t Really a Choice',
  'date' => '2025-05-20',
  'tags' => 
  array (
    0 => 'homelessness',
    1 => 'advocacy',
    2 => 'misconceptions',
    3 => 'personal',
    4 => 'draft',
    5 => 'videoscript',
  ),
  'excerpt' => 'Addressing the misconception that homelessness is a choice and exploring the reality of limited options',
  'categories' => 
  array (
    0 => 'Street Advocacy',
    1 => 'Alienation',
    2 => 'Writings',
  ),
  'thumbnail' => '../../img/siblingTruth.jpg',
  'source_file' => 'content\\street\\when-choice-isnt-choice.md',
);

// Raw content
$post_content = '<p>If you’re reading this, you might be weighing an impossible decision: _Stay in a toxic or unsafe situation, or leave—even if leaving means homelessness._</p>

<p>I’ve been there. I slept in my car, crashed on couches, and washed dishes just to use someone’s WiFi. People called it a "choice," as if I’d picked it off a menu of good options. But sometimes, the only "choice" you have is which kind of hurt you can survive.</p>

<p><strong>If that’s where you are right now, I want you to know three things:</strong></p>

<h3>You Are Not Broken</h3>

<p>Leaving doesn’t mean you failed. For many LGBTQ+ youth (and others in crisis), homelessness isn’t a "bad decision"—it’s the _least terrible_ option in a world that’s failed you.</p>

<p>I grew up in a place that felt like a trap: expensive, isolating, and full of people who didn’t understand me. When I left, I told myself I was choosing freedom—even though freedom meant hunger and uncertainty. It wasn’t a good life, but it was _mine_, and that mattered.</p>

<p><strong>Remember:</strong> Surviving isn’t the same as thriving. You deserve both.</p>

<h3>You Are Not Alone</h3>

<p>People who’ve never been desperate will judge you. They’ll say, _"Why didn’t you just…?"_ as if solutions were obvious. But real help doesn’t come with blame.</p>

<ul><li><strong>Look for the helpers:</strong> Libraries, LGBTQ+ centers, and even some churches offer warmth, WiFi, and connections without strings.</li>
<p><li><strong>Trust small kindnesses:</strong> The friend who lets you shower, the gas station clerk who doesn’t chase you out—these moments matter.</li></p>
<p><li><strong>Ignore the noise:</strong> Your worth isn’t defined by someone else’s shame.</li></p>

<h3>This Is Temporary (Even When It Doesn’t Feel Like It)</h3>

<p>Homelessness isn’t your identity—it’s a chapter. My life changed when I found a community that _chose_ me back. Yours will too.</p>

<p><strong>While you’re in the thick of it:</strong></p>

<p><li><strong>Prioritize safety:</strong> Trust your gut. Avoid isolated areas; sleep near 24-hour stores or shelters if possible.</li></p>
<p><li><strong>Guard your documents:</strong> Keep your ID, birth certificate, and any paperwork in a waterproof bag. They’re your tickets to housing and jobs later.</li></p>
<p><li><strong>Let yourself dream:</strong> Write down what you want your life to look like in five years. Not the "how," just the "what." Hope is fuel.</li></p>

<h3>The Truth No One Tells You</h3>

<p>The world should’ve given you better options. It didn’t. But you’re still here, still fighting—and that means _something_ is working.</p>

<p>One day, you’ll look back and realize:</p>

<p><li><em></em>You weren’t "homeless." You were in transition.*</li></p>
<p><li><strong>You weren’t "reckless." You were brave.</strong></li></p>
<p><li><strong>And you didn’t "choose" this. You outlasted it.</strong></li></ul></p>

<p>Until then, keep going. However you can.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>