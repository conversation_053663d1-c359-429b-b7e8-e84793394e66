<?php
// Auto-generated blog post
// Source: tips-for-schools.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Tips for Schools to Support Families Experiencing Custody Issues';
$meta_description = '\\"Make co-parenting the school\'s policy, not the child\'s burden.\\"';
$meta_keywords = 'alienation, advocacy, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://media.licdn.com/dms/image/D5612AQGd_iF2oBQzsA/article-cover_image-shrink_720_1280/0/1680973280752?e=2147483647&v=beta&t=DqjrGtyYCmPSxOklUSFlJowT3GpKXSkCpFO-EHNQ_Zs';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Tips for Schools to Support Families Experiencing Custody Issues',
  'author' => 'Wendy Perry Co-Parenting & Alienation Support',
  'excerpt' => '\\"Make co-parenting the school\'s policy, not the child\'s burden.\\"',
  'tags' => 
  array (
    0 => 'alienation',
    1 => 'advocacy',
  ),
  'categories' => 
  array (
    0 => 'Alienation',
  ),
  'thumbnail' => 'https://media.licdn.com/dms/image/D5612AQGd_iF2oBQzsA/article-cover_image-shrink_720_1280/0/1680973280752?e=2147483647&v=beta&t=DqjrGtyYCmPSxOklUSFlJowT3GpKXSkCpFO-EHNQ_Zs',
  'source_file' => 'content\\alienation\\tips-for-schools.md',
);

// Raw content
$post_content = '<p>1. Communicate with both parents directly.  Don\'t rely on or expect one parent to pass information on to the other parent.</p>

<p>2. Allow for two households to be on all communication lists, emergency contact cards and directories.  Allow access and log ins for two households for online portals and newsletters.</p>

<p>3. Allow for both parents to attend conferences, events, activities and volunteer opportunities.  Give both parents equal opportunities for participation.</p>

<p>4. Don\'t align with or favor one parent over the other parent.  Don\'t engage in disparaging or gossiping about a parent with the other parent.  Treat both parents equally.</p>

<p>5. Don\'t take directions or orders from a child or a parent regarding excluding the other parent.</p>

<p>6. Don\'t burden students with being the gatekeepers of their parents.  Be an authoritative but neutral zone where students can receive support from both parents.  Children need the support of both parents to be mentally and emotionally healthy.</p>

<p>7. Take directions and actions based on legal documents such as court orders, protective orders and not based on verbal or written statements made by one parent about the other parent.  Uphold a parent\'s legal right to conference, legal right to information, legal right to joint educational decision making, sole right to educational decision making, etc. based on legal documents.</p>

<p>8.  "Make it easy" for the kids to have both parents at events etc.  It\'s okay to have separate conferences or to have the parents sit in different areas if necessary to keep the peace but it\'s not okay to exclude one parent entirely.</p>

<p>9. Make sure your school counseling offices always have parental alienation and co parenting educational materials and resources available for parents and students.  Have your school give all employees formal co-parenting and parental alienation education and training to recognize the signs of parental alienation and to be equipped to help the students dealing with parental alienation (defined as Child Psychological Abuse by the American Psychiatric Association.)</p>

<p>10. Materials, resources and certified training can be provided by Wendy Perry Co-parenting and Parental Alienation Education Services.  Contact information below.</p>

<p>"Make co-parenting the school\'s policy, not the child\'s burden."</p>

<p>Wendy Perry Co-parenting and Parental Alienation Education and Support</p>
<p>Website: www.wendyjperry.com/schools</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>