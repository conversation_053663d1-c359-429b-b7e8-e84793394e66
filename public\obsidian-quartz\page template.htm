<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title ?? 'A. A. Chips'); ?></title>
    
    <?php if (isset($meta_description)): ?>
    <meta name="description" content="<?php echo htmlspecialchars($meta_description); ?>">
    <?php endif; ?>
    
    <?php if (isset($meta_keywords)): ?>
    <meta name="keywords" content="<?php echo htmlspecialchars($meta_keywords); ?>">
    <?php endif; ?>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ($paths['base_path'] ?? '') . 'favicon.ico'; ?>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo $css_path ?? ($paths['css_path'] ?? '') . 'style.css'; ?>">
    <link rel="stylesheet" href="<?php echo ($paths['css_path'] ?? '') . 'comments.css'; ?>">
    <link rel="stylesheet" href="<?php echo ($paths['css_path'] ?? '') . 'visitor-counter.css'; ?>">
    
    <!-- Open Graph / Social Media -->
    <meta property="og:title" content="<?php echo htmlspecialchars($page_title ?? 'A. A. Chips'); ?>">
    <?php if (isset($meta_description)): ?>
    <meta property="og:description" content="<?php echo htmlspecialchars($meta_description); ?>">
    <?php endif; ?>
    <?php if (isset($thumbnail)): ?>
    <meta property="og:image" content="<?php echo htmlspecialchars($thumbnail); ?>">
    <?php endif; ?>
    <meta property="og:type" content="article">
    
    <!-- Additional head content -->
    <?php if (isset($additional_head)): echo $additional_head; endif; ?>
</head>
<body>
    <!-- Header -->
    <?php 
    $headerPath = ($paths['includes_path'] ?? '') . 'header.php';
    if (file_exists($headerPath)) {
        include $headerPath;
    }
    ?>
    
    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <?php 
            // Output the main content
            if (isset($content)) {
                echo $content;
            } else {
                echo '<p>Content not found.</p>';
            }
            ?>
        </div>
    </main>
    
    <!-- Sidebar (if exists) -->
    <?php 
    $sidebarPath = ($paths['includes_path'] ?? '') . 'sidebar.php';
    if (file_exists($sidebarPath)) {
        include $sidebarPath;
    }
    ?>
    
    <!-- Footer -->
    <?php 
    $footerPath = ($paths['includes_path'] ?? '') . 'footer.php';
    if (file_exists($footerPath)) {
        include $footerPath;
    }
    ?>
    
    <!-- JavaScript -->
    <script src="<?php echo $js_path ?? ($paths['js_path'] ?? '') . 'script.js'; ?>"></script>
    <script src="<?php echo ($paths['js_path'] ?? '') . 'color-switcher.js'; ?>"></script>
    <script src="<?php echo ($paths['js_path'] ?? '') . 'comments.js'; ?>"></script>
    <script src="<?php echo ($paths['js_path'] ?? '') . 'donation-modal.js'; ?>"></script>
    
    <!-- Additional scripts -->
    <?php if (isset($additional_scripts)): echo $additional_scripts; endif; ?>
</body>
</html>
