<?php
// Auto-generated blog post
// Source: 10-reasons-i-hate-restaurants.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Ten Reasons I Hate Working in Restaurants';
$meta_description = 'My intention in sharing these observations is not merely to complain but to initiate a discussion about potential structural improvements that could benefit both workers and the industry as a whole.';
$meta_keywords = 'writings, aachips, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://i0.wp.com/worksucks.com/wp-content/uploads/2023/08/577a5-restaurant-work-sucks.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Ten Reasons I Hate Working in Restaurants',
  'author' => 'A. A. Chips',
  'tags' => 
  array (
    0 => 'writings',
    1 => 'aachips',
  ),
  'excerpt' => 'My intention in sharing these observations is not merely to complain but to initiate a discussion about potential structural improvements that could benefit both workers and the industry as a whole.',
  'date' => '2021-04-06',
  'categories' => 
  array (
    0 => 'Writings',
    1 => 'Apple Chip Kitchen',
  ),
  'thumbnail' => 'https://i0.wp.com/worksucks.com/wp-content/uploads/2023/08/577a5-restaurant-work-sucks.jpg',
  'source_file' => 'content\\writings\\10-reasons-i-hate-restaurants.md',
);

// Raw content
$post_content = '<p>Having found that my experiences resonated with others, I wanted to elaborate on the reasons why I, a self-taught chef with a background in social work and someone on the autistic spectrum, dislike working in the conventional food industry. Despite having successfully catered to numerous private clients, my short stints in restaurants have been consistently negative.</p>

<p>Note: Because of these reasons, I don\'t even like eating at restaurants.</p>

<p>Here are ten key issues I\'ve encountered:</p>

<p>1. Tipping Practices: The reliance on tips, often supplementing meager hourly wages, feels akin to a commission-based pyramid scheme. Expecting customers to directly subsidize a worker\'s basic living expenses is unreasonable. It places the burden of ensuring a living wage on the patron\'s generosity rather than the employer\'s responsibility, creating instability for employees.</p>

<p>2. Hostile Environments: Restaurants frequently cultivate environments unwelcoming to certain individuals. While some exclusion, such as preventing loitering, might be argued as a business necessity, the lack of consideration for conditions like autism is problematic. The sensory overload and inflexible demands common in restaurant kitchens can be deeply challenging for neurodivergent individuals. A more inclusive approach, such as adapting roles to accommodate different sensitivities and skills, would be beneficial.</p>

<p>3. Incompetent Management: Bottom-level employees are often at the mercy of poor management. Inconsistent schedules, understaffing leading to added responsibilities, and last-minute task assignments due to managerial procrastination are all too common.</p>

<p>4. Prevalence of Drug Use: The culture in many restaurants normalizes and even encourages drug use. Limited breaks often cater primarily to smokers, creating pressure to adopt the habit. The constant presence of alcohol can be difficult for those in recovery. Furthermore, the demanding and high-pressure nature of kitchen work can lead to reliance on stimulants, ranging from excessive caffeine to more harmful substances. The underlying reasons for this higher rate of drug use within the profession deserve scrutiny.</p>

<p>5. Excessive Waste: The operational model of many restaurants, with complex menus requiring numerous pre-prepared ingredients, inherently leads to significant food waste. While legal protections exist for food donation, the convenience of disposal often outweighs the effort of coordination. This "better safe than sorry" approach to stocking ingredients, coupled with limited options for staff to take home leftovers, results in perfectly good food being discarded. Simpler menus with fewer specialty items would likely mitigate this issue.</p>

<p>6. "Machismo" and Lack of Professionalism: Unfortunately, a "boys club" mentality persists in many higher-level restaurant kitchens. This can manifest in unprofessional behavior, including instances of assault and the promotion of less-qualified individuals based on gender. Such environments prioritize protecting the in-group, even at the expense of the business and individual safety.</p>

<p>7. Overpriced Menu Items: Many restaurant dishes can be replicated at home with basic culinary skills, offering significant cost savings for those who frequently dine out. While some dishes require specialized equipment or techniques, the markup on many menu items is substantial.</p>

<p>8. Unsatisfying Portions: There\'s a growing trend towards smaller, more elaborately presented dishes rather than generous servings. For individuals with larger appetites, these portions can be disappointing.</p>

<p>9. Health Code Violations: Having worked in kitchens, I am privy to practices that violate health codes. This can range from simple negligence to deliberate concealment of pest infestations and unsanitary conditions. The potential for spiteful actions by disgruntled employees regarding food preparation is also a concern.</p>

<p>10. COVID-19 Safety Concerns: The pandemic has highlighted the precarious position of restaurant workers and the potential risks to public health. The pressure for restaurants to remain open, even when unsafe, has exposed workers to increased risk of infection. For individuals with pre-existing health conditions, this disregard for safety is deeply concerning.</p>

<p>Despite these issues, chefs hold significant social influence and have the potential to advocate for positive change within the industry, promoting better labor practices, environmental sustainability, and public well-being.</p>

<p>My intention in sharing these observations is not merely to complain but to initiate a discussion about potential structural improvements that could benefit both workers and the industry as a whole.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>