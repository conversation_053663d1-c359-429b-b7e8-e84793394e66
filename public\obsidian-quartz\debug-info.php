<?php
echo "<h1>Debug Information</h1>";

echo "<h2>1. File Timestamps</h2>";
echo "CSS file modified: " . date('Y-m-d H:i:s', filemtime('css/style.css')) . "<br>";
echo "i-am-not-smee.php modified: " . date('Y-m-d H:i:s', filemtime('content/i-am-not-smee.php')) . "<br>";
echo "sidebar.php modified: " . date('Y-m-d H:i:s', filemtime('includes/sidebar.php')) . "<br>";

echo "<h2>2. CSS Content Check</h2>";
$css_content = file_get_contents('css/style.css');
if (strpos($css_content, '#4a3d6b') !== false) {
    echo "✅ CSS contains new darker purple color (#4a3d6b)<br>";
} else {
    echo "❌ CSS does not contain new color<br>";
}

echo "<h2>3. Image File Check</h2>";
if (file_exists('img/Pasted image 20250515100439.png')) {
    echo "✅ Image file exists<br>";
    echo "Image size: " . filesize('img/Pasted image 20250515100439.png') . " bytes<br>";
} else {
    echo "❌ Image file not found<br>";
}

echo "<h2>4. PHP Content Check</h2>";
$php_content = file_get_contents('content/i-am-not-smee.php');
if (strpos($php_content, '<img src="img/Pasted image') !== false) {
    echo "✅ PHP file contains proper img tag<br>";
} else {
    echo "❌ PHP file does not contain img tag<br>";
}

echo "<h2>5. Server Info</h2>";
echo "Current time: " . date('Y-m-d H:i:s') . "<br>";
echo "PHP version: " . phpversion() . "<br>";

// Check for opcache
if (function_exists('opcache_get_status')) {
    $opcache = opcache_get_status();
    if ($opcache['opcache_enabled']) {
        echo "⚠️ OPcache is enabled - this might cache PHP files<br>";
        echo "<a href='?clear_opcache=1'>Click to clear OPcache</a><br>";
    }
}

if (isset($_GET['clear_opcache']) && function_exists('opcache_reset')) {
    opcache_reset();
    echo "✅ OPcache cleared!<br>";
}
?>
