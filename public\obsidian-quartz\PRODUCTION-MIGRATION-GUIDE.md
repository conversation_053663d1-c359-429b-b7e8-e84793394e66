# Production Migration Guide

This guide will help you migrate your local database setup to your production web host.

## 🏠 Local Development Setup (Completed)

✅ **Database Created:** `aachipsc` with all required tables  
✅ **Comments System:** Working with simple name/email authentication  
✅ **Visitor Counter:** Tracking page visits and statistics  
✅ **Configuration:** Using default XAMPP settings (localhost, root, no password)

## 🌐 Production Migration Steps

### Step 1: Prepare Production Database

1. **Log into your web host control panel** (cPanel, Plesk, etc.)
2. **Create a new MySQL database:**
   - Database name: `your_username_aachipsc` (or similar)
   - Note down the database name, username, and password
3. **Import the database schema:**
   - Use phpMyAdmin or your host's database tool
   - Import both SQL files:
     - `comments/aachipsc_comments.sql`
     - `visitor-counter/visitor_counter.sql`

### Step 2: Create Secure Configuration

Create a file outside your public_html directory:
**Location:** `../secure_config/obq_comments.php`

```php
<?php
// Secure configuration for production
if (!defined('OBQ_CONFIG_ACCESS')) {
    exit('Direct access not allowed');
}

return [
    'database' => [
        'host' => 'localhost',                    // Usually localhost
        'dbname' => 'your_username_aachipsc',    // Your actual database name
        'username' => 'your_db_username',        // Your database username
        'password' => 'your_secure_password',    // Your database password
        'charset' => 'utf8mb4',
        'table_prefix' => 'aachipsc_blog_',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    ],
    'auth' => [
        'session_timeout' => 86400 * 7,          // 7 days
        'require_email_verification' => false,   // Set to true if you want email verification
    ]
];
```

### Step 3: Upload Files

1. **Upload your blog files** to your web host
2. **Ensure the secure config file** is outside public_html
3. **Set proper file permissions:**
   - Directories: 755
   - PHP files: 644
   - Config file: 600 (more secure)

### Step 4: Test Production Setup

1. **Visit your production site**
2. **Test database connection:**
   - Go to `yoursite.com/path/to/test-database.php`
   - Should show all green checkmarks
3. **Test comments system:**
   - Try posting a comment on a blog post
4. **Test visitor counter:**
   - Check if page visit counts are working

### Step 5: Configure Production Settings

Edit `comments/config.php` for production:

```php
// In the merged config array, update these for production:
'security' => [
    'secure_cookies' => true,        // Enable for HTTPS
    'same_site_cookies' => 'Strict', // More secure for production
],

'admin' => [
    'notification_email' => '<EMAIL>',
    'enable_email_notifications' => true,  // Enable if you want email alerts
],

'display' => [
    'timezone' => 'Your/Timezone',   // Set your actual timezone
],
```

## 🔧 Troubleshooting

### Database Connection Issues

1. **Check database credentials** in your secure config file
2. **Verify database exists** in your hosting control panel
3. **Check if tables were imported** correctly
4. **Contact your host** if connection still fails

### Permission Issues

```bash
# Set correct permissions (if you have SSH access)
chmod 755 public_html/
chmod 644 public_html/*.php
chmod 600 ../secure_config/obq_comments.php
```

### Common Host-Specific Issues

- **Shared hosting:** Database host might not be 'localhost'
- **Some hosts require:** Full database name including username prefix
- **File paths:** May need adjustment for your host's directory structure

## 📊 Database Maintenance

### Regular Maintenance Tasks

```sql
-- Clean old visit data (keep last 365 days)
CALL CleanOldVisitData(365);

-- Check database size
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'your_database_name'
AND table_name LIKE 'aachipsc_blog_%';
```

### Backup Strategy

1. **Regular database backups** through your hosting control panel
2. **Export SQL dumps** periodically
3. **Test restore process** to ensure backups work

## 🚀 Going Live Checklist

- [ ] Production database created and imported
- [ ] Secure config file created with production credentials
- [ ] Files uploaded to production server
- [ ] File permissions set correctly
- [ ] Database connection test passes
- [ ] Comments system working
- [ ] Visitor counter tracking visits
- [ ] Email notifications configured (if desired)
- [ ] Backup strategy in place

## 🔄 Syncing Local and Production

### From Local to Production
1. Export local database changes
2. Import to production database
3. Upload any new/modified files

### From Production to Local
1. Export production database
2. Import to local XAMPP database
3. Download any new files

---

**Need Help?** Check your web host's documentation or contact their support team for database-specific configuration details.
