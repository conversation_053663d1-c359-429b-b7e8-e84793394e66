<?php
/**
 * Database Setup Script for A. A. Chips' Blog
 * Sets up both comments and visitor counter systems
 * Designed for XAMPP local development
 */

// Prevent direct access from web
if (php_sapi_name() !== 'cli' && !isset($_GET['web_setup'])) {
    echo "This script should be run from command line. Add ?web_setup=1 to run from browser.";
    exit(1);
}

echo "=== A. A. Chips' Blog Database Setup ===\n\n";

// Configuration for XAMPP
$config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'aachipsc',
    'charset' => 'utf8mb4'
];

try {
    echo "Step 1: Testing MySQL connection...\n";
    
    // Test basic MySQL connection
    $pdo = new PDO("mysql:host={$config['host']}", $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ MySQL connection successful\n";
    
    // Check if database exists, create if not
    echo "\nStep 2: Checking database '{$config['database']}'...\n";
    $stmt = $pdo->query("SHOW DATABASES LIKE '{$config['database']}'");
    if (!$stmt->fetch()) {
        echo "Database '{$config['database']}' not found. Creating...\n";
        $pdo->exec("CREATE DATABASE {$config['database']} CHARACTER SET {$config['charset']} COLLATE {$config['charset']}_unicode_ci");
        echo "✓ Database '{$config['database']}' created\n";
    } else {
        echo "✓ Database '{$config['database']}' exists\n";
    }
    
    // Connect to the specific database
    $pdo = new PDO("mysql:host={$config['host']};dbname={$config['database']};charset={$config['charset']}", 
                   $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "\nStep 3: Setting up comments system tables...\n";

    // Read and execute comments SQL
    $commentsSql = file_get_contents(__DIR__ . '/comments/aachipsc_comments.sql');
    if (!$commentsSql) {
        throw new Exception("Could not read comments SQL file");
    }

    // Remove the USE statement since we're already connected to the database
    $commentsSql = preg_replace('/^USE\s+\w+;\s*$/m', '', $commentsSql);

    // Remove comments and empty lines
    $lines = explode("\n", $commentsSql);
    $cleanLines = [];
    foreach ($lines as $line) {
        $line = trim($line);
        if (!empty($line) && !preg_match('/^\s*--/', $line)) {
            $cleanLines[] = $line;
        }
    }
    $commentsSql = implode("\n", $cleanLines);

    // Split by semicolons and execute each statement
    $statements = array_filter(array_map('trim', explode(';', $commentsSql)));
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                echo "  ✓ Executed: " . substr($statement, 0, 50) . "...\n";
            } catch (Exception $e) {
                echo "  ✗ Failed: " . substr($statement, 0, 50) . "... Error: " . $e->getMessage() . "\n";
            }
        }
    }
    echo "✓ Comments system tables created\n";
    
    echo "\nStep 4: Setting up visitor counter tables...\n";

    // Read and execute visitor counter SQL
    $visitorSql = file_get_contents(__DIR__ . '/visitor-counter/visitor_counter.sql');
    if (!$visitorSql) {
        throw new Exception("Could not read visitor counter SQL file");
    }

    // Handle the visitor counter SQL more carefully
    // First, execute everything before the stored procedures
    $beforeProcedures = preg_split('/DELIMITER\s+\/\//', $visitorSql)[0];

    // Remove comments and empty lines
    $lines = explode("\n", $beforeProcedures);
    $cleanLines = [];
    foreach ($lines as $line) {
        $line = trim($line);
        if (!empty($line) && !preg_match('/^\s*--/', $line)) {
            $cleanLines[] = $line;
        }
    }
    $beforeProcedures = implode("\n", $cleanLines);

    $statements = array_filter(array_map('trim', explode(';', $beforeProcedures)));
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                // Fix the page_stats table creation issue
                if (strpos($statement, 'aachipsc_blog_page_stats') !== false) {
                    $statement = "CREATE TABLE IF NOT EXISTS aachipsc_blog_page_stats (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        page_slug VARCHAR(255) NOT NULL UNIQUE,
                        page_title VARCHAR(500) DEFAULT NULL,
                        total_visits INT DEFAULT 0,
                        unique_visits INT DEFAULT 0,
                        today_visits INT DEFAULT 0,
                        today_unique_visits INT DEFAULT 0,
                        last_visit TIMESTAMP NULL,
                        first_visit TIMESTAMP NULL,
                        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_page_slug (page_slug),
                        INDEX idx_total_visits (total_visits),
                        INDEX idx_unique_visits (unique_visits),
                        INDEX idx_last_visit (last_visit)
                    )";
                }
                $pdo->exec($statement);
                echo "  ✓ Executed: " . substr($statement, 0, 50) . "...\n";
            } catch (Exception $e) {
                echo "  ✗ Failed: " . substr($statement, 0, 50) . "... Error: " . $e->getMessage() . "\n";
            }
        }
    }

    // For stored procedures, we'll create them manually to avoid delimiter issues
    // UpdatePageStats procedure
    $updatePageStatsProc = "
    CREATE OR REPLACE PROCEDURE UpdatePageStats(IN p_page_slug VARCHAR(255))
    BEGIN
        DECLARE total_count INT DEFAULT 0;
        DECLARE unique_count INT DEFAULT 0;
        DECLARE today_count INT DEFAULT 0;
        DECLARE today_unique_count INT DEFAULT 0;
        DECLARE first_visit_time TIMESTAMP DEFAULT NULL;
        DECLARE last_visit_time TIMESTAMP DEFAULT NULL;

        SELECT COUNT(*) INTO total_count
        FROM aachipsc_blog_page_visits
        WHERE page_slug = p_page_slug;

        SELECT COUNT(DISTINCT CONCAT(ip_address, user_agent_hash)) INTO unique_count
        FROM aachipsc_blog_page_visits
        WHERE page_slug = p_page_slug;

        SELECT COUNT(*) INTO today_count
        FROM aachipsc_blog_page_visits
        WHERE page_slug = p_page_slug AND visit_date = CURDATE();

        SELECT COUNT(DISTINCT CONCAT(ip_address, user_agent_hash)) INTO today_unique_count
        FROM aachipsc_blog_page_visits
        WHERE page_slug = p_page_slug AND visit_date = CURDATE();

        SELECT MIN(visit_timestamp), MAX(visit_timestamp) INTO first_visit_time, last_visit_time
        FROM aachipsc_blog_page_visits
        WHERE page_slug = p_page_slug;

        INSERT INTO aachipsc_blog_page_stats
            (page_slug, total_visits, unique_visits, today_visits, today_unique_visits, first_visit, last_visit)
        VALUES
            (p_page_slug, total_count, unique_count, today_count, today_unique_count, first_visit_time, last_visit_time)
        ON DUPLICATE KEY UPDATE
            total_visits = total_count,
            unique_visits = unique_count,
            today_visits = today_count,
            today_unique_visits = today_unique_count,
            first_visit = COALESCE(first_visit, first_visit_time),
            last_visit = last_visit_time;
    END";

    $pdo->exec($updatePageStatsProc);

    // CleanOldVisitData procedure
    $cleanOldDataProc = "
    CREATE OR REPLACE PROCEDURE CleanOldVisitData(IN days_to_keep INT)
    BEGIN
        DELETE FROM aachipsc_blog_page_visits
        WHERE visit_timestamp < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);

        DELETE FROM aachipsc_blog_daily_stats
        WHERE stat_date < DATE_SUB(CURDATE(), INTERVAL days_to_keep DAY);
    END";

    $pdo->exec($cleanOldDataProc);
    
    echo "✓ Visitor counter tables created\n";
    
    echo "\nStep 5: Verifying table creation...\n";

    $requiredTables = [
        'aachipsc_blog_users',
        'aachipsc_blog_comments',
        'aachipsc_blog_comment_votes',
        'aachipsc_blog_spam_patterns',
        'aachipsc_blog_rate_limits',
        'aachipsc_blog_page_visits',
        'aachipsc_blog_page_stats',
        'aachipsc_blog_site_stats',
        'aachipsc_blog_visitor_sessions',
        'aachipsc_blog_daily_stats'
    ];
    
    $stmt = $pdo->query("SHOW TABLES");
    $existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $missingTables = [];
    foreach ($requiredTables as $table) {
        if (in_array($table, $existingTables)) {
            echo "✓ Table exists: $table\n";
        } else {
            echo "✗ Table missing: $table\n";
            $missingTables[] = $table;
        }
    }
    
    if (empty($missingTables)) {
        echo "\n✅ All required tables created successfully!\n";
    } else {
        echo "\n❌ Some tables are missing. Please check the SQL files.\n";
        exit(1);
    }
    
    echo "\nStep 6: Testing database connections...\n";
    
    // Test comments system
    try {
        require_once __DIR__ . '/comments/database.php';
        $db = CommentDatabase::getInstance();
        echo "✓ Comments system connection working\n";
    } catch (Exception $e) {
        echo "✗ Comments system connection failed: " . $e->getMessage() . "\n";
    }
    
    // Test visitor counter
    try {
        require_once __DIR__ . '/visitor-counter/VisitorCounter.php';
        $counter = new VisitorCounter($db);
        echo "✓ Visitor counter system working\n";
    } catch (Exception $e) {
        echo "✗ Visitor counter system failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n🎉 Database setup completed successfully!\n\n";
    echo "=== Next Steps ===\n";
    echo "1. Your database is ready for local development\n";
    echo "2. Comments system is configured for simple name/email auth\n";
    echo "3. Visitor counter will track page visits automatically\n";
    echo "4. Check your blog pages to see the systems in action\n\n";
    echo "=== Database Info ===\n";
    echo "Database: {$config['database']}\n";
    echo "Host: {$config['host']}\n";
    echo "Username: {$config['username']}\n";
    echo "Password: (empty for XAMPP default)\n\n";
    echo "=== For Production ===\n";
    echo "Create a secure config file with your production database credentials\n";
    echo "Location: ../secure_config/obq_comments.php\n";
    
} catch (Exception $e) {
    echo "\n❌ Setup failed: " . $e->getMessage() . "\n";
    echo "Please check your XAMPP installation and try again.\n";
    exit(1);
}
