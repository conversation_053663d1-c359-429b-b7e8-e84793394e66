<?php
// Auto-generated category index
// Category: climate

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Climate';
$meta_description = 'Browse all posts in the Climate category';
$meta_keywords = 'climate, posts, A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$related_posts = [];

// Category posts data
$category_posts = array (
  0 => 
  array (
    'title' => 'Standing Rock Stories',
    'author' => NULL,
    'date' => '2016-12-10',
    'excerpt' => 'Personal stories and reflections from the Standing Rock movement',
    'url' => 'standing-rock.php',
    'tags' => 
    array (
      0 => 'climate',
      1 => 'journal',
    ),
    'filename' => 'standing-rock',
    'thumbnail' => '../../img/edutainment/notvotingforthisamerica.jpg',
  ),
  1 => 
  array (
    'title' => 'Pope Francis Climate Talking Points',
    'author' => 'A. A. Chips',
    'date' => '2015-09-28',
    'excerpt' => 'I went six days without eating. In the days prior to the visit, I was biking in the city, and ran into somebody who saw my t-shirt and offered two tickets to see the Pope. Here are some boiled down ideas from their Encyclical on the Environment we hunger striked over.',
    'url' => 'encyclical-environment.php',
    'tags' => 
    array (
      0 => 'climate',
      1 => 'masscommunication',
      2 => 'homeless',
      3 => 'advocacy',
      4 => 'CompassionateCities',
    ),
    'filename' => 'encyclical-environment',
    'thumbnail' => '../../img/edutainment/popeFrancisClimatePoints.jpg',
  ),
  2 => 
  array (
    'title' => 'Marchers for climate change have a new dream for America - Zuni',
    'author' => 'Vida Volkert - Gallup Independent',
    'date' => '2014-05-08',
    'excerpt' => 'The last of the marchers appeared on the horizon, defying the odds. The young man was not only walking with crutches - he was barefoot. His female companion kept his pace and communicated with him with her hands. She could not talk.',
    'url' => 'new-dream-america.php',
    'tags' => 
    array (
      0 => 'climate',
      1 => 'march',
      2 => 'history',
      3 => 'journal',
    ),
    'filename' => 'new-dream-america',
    'thumbnail' => '../../img/self/desert.jpg',
  ),
  3 => 
  array (
    'title' => 'The Eco-Sattva Vows',
    'author' => 'Joanna Macy & Chris Johnstone',
    'date' => NULL,
    'excerpt' => 'I vow to myself and to each of you: To commit myself daily to the healing of the world. And the welfare of all beings. To live on earth more lightly and less violently \\t\\tin the food, products, and energy I consume.',
    'url' => 'ecosattva.php',
    'tags' => 
    array (
      0 => 'climate',
      1 => 'advocacy',
    ),
    'filename' => 'ecosattva',
    'thumbnail' => '../../img/edutainment/ecosattva.jpg',
  ),
);

// Generate content
ob_start();
?>
<div class="category-index">
    <header class="category-header">
        <h1><?php echo htmlspecialchars($page_title); ?></h1>
        <p class="category-description">All posts in the <?php echo htmlspecialchars($page_title); ?> category</p>
    </header>

    <div class="post-grid">
        <?php foreach ($category_posts as $post): ?>
            <div class="post-card">
                <a href="<?php echo htmlspecialchars($post['url']); ?>" class="post-card-link">
                <div class="post-card-thumbnail">
                    <?php if (isset($post['thumbnail']) && $post['thumbnail']): ?>
                        <?php if (preg_match('/^https?:\/\//', $post['thumbnail'])): ?>
                            <img src="<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php else: ?>
                            <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php endif; ?>
                    <?php else: ?>
                        <?php $placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>
                        <?php $random_placeholder = $placeholder_images[array_rand($placeholder_images)]; ?>
                        <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo $random_placeholder; ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img placeholder">
                    <?php endif; ?>
                </div>
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars(is_string($post['excerpt']) ? $post['excerpt'] : ''); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post['author']) && $post['author'] && is_string($post['author'])): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post['date']) && $post['date']): ?>
                            <span class="post-date"><?php echo is_string($post['date']) ? htmlspecialchars($post['date']) : ''; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>