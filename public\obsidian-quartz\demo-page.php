<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Demo - A. A. Chips' Blog</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 0 auto; 
            padding: 20px; 
            line-height: 1.6;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .visitor-counter {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .comments-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #eee;
        }
    </style>
</head>
<body>
    <h1>🎉 Database Demo Page</h1>
    <p>This page demonstrates the working comments and visitor counter systems.</p>

    <?php
    try {
        // Initialize visitor counter
        require_once 'comments/database.php';
        require_once 'visitor-counter/VisitorCounter.php';
        
        $db = CommentDatabase::getInstance();
        $counter = new VisitorCounter($db);
        
        // Record this page visit
        $pageSlug = 'demo-page';
        $pageTitle = 'Database Demo Page';
        $counter->recordVisit($pageSlug, $pageTitle);
        
        // Get visitor statistics
        $stats = $counter->getPageStats($pageSlug);
        $siteStats = $counter->getSiteStats();
        
        echo '<div class="visitor-counter">';
        echo '<h2>📊 Visitor Statistics</h2>';
        if ($stats) {
            echo '<p><strong>This Page:</strong></p>';
            echo '<ul>';
            echo '<li>Total visits: <span class="info">' . $stats['total_visits'] . '</span></li>';
            echo '<li>Unique visitors: <span class="info">' . $stats['unique_visits'] . '</span></li>';
            echo '<li>Today\'s visits: <span class="info">' . $stats['today_visits'] . '</span></li>';
            echo '<li>First visit: <span class="info">' . ($stats['first_visit'] ? date('M j, Y g:i A', strtotime($stats['first_visit'])) : 'Just now') . '</span></li>';
            echo '<li>Last visit: <span class="info">' . ($stats['last_visit'] ? date('M j, Y g:i A', strtotime($stats['last_visit'])) : 'Just now') . '</span></li>';
            echo '</ul>';
        }
        
        if ($siteStats) {
            echo '<p><strong>Site-wide:</strong></p>';
            echo '<ul>';
            echo '<li>Total site visits: <span class="info">' . $siteStats['total_site_visits'] . '</span></li>';
            echo '<li>Unique visitors: <span class="info">' . $siteStats['unique_site_visitors'] . '</span></li>';
            echo '</ul>';
        }
        echo '</div>';
        
        echo '<div class="success">✅ Visitor counter is working perfectly!</div>';
        
    } catch (Exception $e) {
        echo '<div class="error">❌ Visitor counter error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
    ?>

    <div class="comments-section">
        <h2>💬 Comments System</h2>
        <p>The comments system is ready to use! Here's what's available:</p>
        <ul>
            <li>✅ Simple name/email authentication (no registration required)</li>
            <li>✅ Threaded comments and replies</li>
            <li>✅ Like/dislike voting system</li>
            <li>✅ Spam detection and rate limiting</li>
            <li>✅ Admin moderation capabilities</li>
        </ul>
        
        <?php
        try {
            // Test comments database connection
            $commentCount = $db->getCommentCount('demo-page');
            echo '<p><strong>Comments on this page:</strong> <span class="info">' . $commentCount . '</span></p>';
            echo '<div class="success">✅ Comments system is working perfectly!</div>';
        } catch (Exception $e) {
            echo '<div class="error">❌ Comments system error: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        ?>
        
        <p><em>To add comments to your blog posts, include the comments display component in your page templates.</em></p>
    </div>

    <h2>🚀 Next Steps</h2>
    <ol>
        <li><strong>Local Development:</strong> Your database is fully set up and working!</li>
        <li><strong>Add Comments:</strong> Include the comments system in your blog post templates</li>
        <li><strong>Customize:</strong> Adjust settings in <code>comments/config.php</code></li>
        <li><strong>Production:</strong> Follow the <code>PRODUCTION-MIGRATION-GUIDE.md</code> when ready to go live</li>
    </ol>

    <h2>📁 Key Files</h2>
    <ul>
        <li><code>setup-database.php</code> - Database setup script (run once)</li>
        <li><code>comments/database.php</code> - Database connection and functions</li>
        <li><code>comments/config.php</code> - Configuration settings</li>
        <li><code>visitor-counter/VisitorCounter.php</code> - Visitor tracking class</li>
        <li><code>PRODUCTION-MIGRATION-GUIDE.md</code> - Guide for going live</li>
    </ul>

    <p><small>🕒 Page generated at: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
