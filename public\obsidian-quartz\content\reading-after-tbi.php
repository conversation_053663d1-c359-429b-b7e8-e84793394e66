<?php
// Auto-generated blog post
// Source: reading-after-tbi.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'My Reading Journey after Traumatic Brain Injury';
$meta_description = 'My relationship with reading has fundamentally changed after a series of traumatic brain injuries. Dense blocks of text present a genuine barrier to my learning.';
$meta_keywords = 'journal, personal, readings, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://www.nevinslibrary.org/wp-content/uploads/2016/12/<EMAIL>';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'My Reading Journey after Traumatic Brain Injury',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'tags' => 
  array (
    0 => 'journal',
    1 => 'personal',
    2 => 'readings',
  ),
  'excerpt' => 'My relationship with reading has fundamentally changed after a series of traumatic brain injuries. Dense blocks of text present a genuine barrier to my learning.',
  'categories' => 
  array (
    0 => 'Journal',
    1 => 'Personal Reflections',
  ),
  'thumbnail' => 'https://www.nevinslibrary.org/wp-content/uploads/2016/12/<EMAIL>',
  'source_file' => 'content\\reading-after-tbi.md',
);

// Raw content
$post_content = '<h2>Reading after Traumatic Brain Injury</h2>

<p>As a kid I hated reading. As a young adult I loved reading. Now I have trouble reading.</p>
<p>I am always dedicated to learning new things and improve myself. I enjoy everything about reading. But it\'s gotten to the point where I haven\'t touched my books in a few years.</p>

<p>After a series of traumatic brain injuries, it\'s been harder to focus on reading text. I can look at the text for a few minutes, and realize I haven\'t read a word. Multiple incidents—from physical assaults to pedestrian bike accidents—left me with lasting cognitive challenges. Now, dense blocks of text present a genuine barrier to my learning.</p>

<h2>The New Reality</h2>

<p>My relationship with reading has fundamentally changed:</p>

<ul><li>Loud noises, crowds, and confrontational situations trigger mental breakdowns</li>
<p><li>Processing long text passages has become significantly more difficult</li></p>
<p><li>Traditional textbooks represent both a physical and cognitive burden</li></p>

<p>This isn\'t about devaluing literacy—it\'s about recognizing diverse learning needs in our digital age.</p>

<h2>Beyond the Financial Burden</h2>

<p>While cost is often discussed (and rightfully so), my concern extends beyond money:</p>

<p><li>I don\'t want bulky, heavy books occupying my limited space</li></p>
<p><li>The physical presence of books I struggle to read becomes a constant reminder of my limitations</li></p>
<p><li>For materials I\'ll only reference once or twice, purchasing a full textbook is wasteful</li></ul></p>

<h2>Practical Alternatives for Educators</h2>

<p>Instead of mandatory textbook purchases, consider:</p>

<p>1. <strong>Require texts with e-book options</strong> - Digital formats work with screen readers and accessibility tools</p>
<p>2. <strong>Share essential excerpts</strong> - If only specific sections are needed, provide digital copies of those pages</p>
<p>3. <strong>Embrace student-discovered resources</strong> - Students often find excellent open-source materials that explain concepts in beginner-friendly ways</p>
<p>4. <strong>Create accessible assignment prompts</strong> - Don\'t tie assignments to specific textbook pages without providing alternatives</p>
<p>5. <strong>Remember accessibility is equity</strong> - Required textbooks can create barriers for first-generation students, those with disabilities, and those facing financial hardships</p>

<h2>A Simple Request</h2>

<p>You don\'t need to announce that textbooks are optional. Just provide alternative pathways for students who face barriers—whether financial, physical, or cognitive.</p>

<p>The future of education isn\'t about abandoning valuable resources—it\'s about making knowledge accessible to everyone, regardless of their circumstances.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>