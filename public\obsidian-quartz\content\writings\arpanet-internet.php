<?php
// Auto-generated blog post
// Source: arpanet-internet.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'The Birth of the Internet - From ARPANET to TikTok';
$meta_description = 'A brief history of the internet, from ARPANET to TikTok.';
$meta_keywords = 'internet, history, writings, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://images.theconversation.com/files/144166/original/image-20161102-27243-uve388.jpg?ixlib=rb-1.1.0&q=30&auto=format&w=754&h=588&fit=crop&dpr=2';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'The Birth of the Internet - From ARPANET to TikTok',
  'author' => 'A. A. Chips',
  'date' => '2025-06-06',
  'tags' => 
  array (
    0 => 'internet',
    1 => 'history',
    2 => 'writings',
  ),
  'excerpt' => 'A brief history of the internet, from ARPANET to TikTok.',
  'categories' => 
  array (
    0 => 'Writings',
  ),
  'thumbnail' => 'https://images.theconversation.com/files/144166/original/image-20161102-27243-uve388.jpg?ixlib=rb-1.1.0&q=30&auto=format&w=754&h=588&fit=crop&dpr=2',
  'source_file' => 'content\\writings\\arpanet-internet.md',
);

// Raw content
$post_content = '<p>"You\'ve Got Mail!" If you\'re anything like me, you might remember that iconic greeting from America Online, making you think that\'s where the internet began. But guess what? The real story is way cooler—and it all started with a super intense competition called the Cold War.From Top Secret Military Tech to Your Favorite TikToks</p>

<p>Imagine a time when computers were giant monsters that took up whole rooms! These super-sized machines could only do one thing at a time, and sometimes it took weeks just to finish a calculation. Universities eventually got their hands on some of these, but they were all isolated, like islands.</p>

<h2>From Military Necessity to Global Connection</h2>

<p>Then came ARPANET—that\'s the Advanced Research Projects Agency Network. Picture this: the Department of Defense created it in the late 1960s. Why? To link up smart scientists and engineers across the US. This project, funded by the military, was all about giving America a major advantage over the Soviet Union during the Cold War.</p>

<p>Now, this early network was nothing like the internet you use today. To send a message from one computer to another, they literally had to plug in physical cables, and technicians had to look up server addresses in huge printed books. It sounds super clunky, right? But back then, it was totally mind-blowing.From ARPANET to the World Wide Web</p>

<h2>From ARPANET to Internet</h2>

<p>What started as a secret military project grew into the amazing global network we all use every single day. The journey from ARPANET to our modern internet is one of the biggest leaps humanity has ever made. It\'s like how military inventions sometimes become everyday stuff we can\'t live without.</p>

<p>Today, when you scroll through Instagram or hop on a video call with your friends, your data zooms across continents in milliseconds! It travels through underwater fiber optic cables and even bounces off satellites way up in space. All these complicated systems work so smoothly that we barely even think about the incredible tech making it all happen.</p>

<p>Want to dive even deeper into internet history? Check out this awesome mini-documentary from SciShow: [Link to SciShow video here!]</p>

<iframe width="560" height="315" src="https://www.youtube.com/embed/1UStbvRnwmQ?si=zvKCuVPP6GbhcOn4" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

<p>Even super smart web developers don\'t know every single detail about how early "packet switching" or "DNS servers" worked. But what\'s important is to appreciate this revolutionary communication system. It was born from a Cold War rivalry and totally transformed into the connected world we know today. Pretty wild, huh?</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>