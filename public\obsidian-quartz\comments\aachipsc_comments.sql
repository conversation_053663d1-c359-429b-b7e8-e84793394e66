-- Comments System Database Schema for A. A. Chips' Blog
-- Database: aachipsc
-- Table prefix: aachipsc_blog_

USE aachipsc;

-- Users table for simple name/email authentication
CREATE TABLE IF NOT EXISTS aachipsc_blog_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_banned BOOLEAN DEFAULT FALSE,
    UNIQUE KEY unique_email_name (email, name),
    INDEX idx_email (email),
    INDEX idx_name (name)
);

-- Comments table (matches your existing aachipsc_blog_comments table)
CREATE TABLE IF NOT EXISTS aachipsc_blog_comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    post_slug VARCHAR(255) NOT NULL,
    user_id INT NOT NULL,
    parent_id INT NULL, -- For threaded comments/replies
    content TEXT NOT NULL,
    is_approved BOOLEAN DEFAULT TRUE, -- For moderation
    is_spam BOOLEAN DEFAULT FALSE,
    spam_score DECIMAL(3,2) DEFAULT 0.00, -- 0.00 to 1.00
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    ip_address VARCHAR(45), -- IPv6 compatible
    user_agent TEXT,
    FOREIGN KEY (user_id) REFERENCES aachipsc_blog_users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES aachipsc_blog_comments(id) ON DELETE CASCADE,
    INDEX idx_post_slug (post_slug),
    INDEX idx_user_id (user_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_created_at (created_at),
    INDEX idx_approved_spam (is_approved, is_spam)
);

-- Comment votes table (for like/dislike functionality)
CREATE TABLE IF NOT EXISTS aachipsc_blog_comment_votes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    comment_id INT NOT NULL,
    user_id INT NOT NULL,
    vote_type ENUM('like', 'dislike') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (comment_id) REFERENCES aachipsc_blog_comments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES aachipsc_blog_users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_vote (comment_id, user_id),
    INDEX idx_comment_id (comment_id),
    INDEX idx_user_id (user_id)
);

-- Spam detection patterns table
CREATE TABLE IF NOT EXISTS aachipsc_blog_spam_patterns (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pattern VARCHAR(500) NOT NULL,
    pattern_type ENUM('keyword', 'regex', 'url') NOT NULL,
    weight DECIMAL(3,2) DEFAULT 0.50, -- How much this pattern contributes to spam score
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_pattern_type (pattern_type),
    INDEX idx_active (is_active)
);

-- Rate limiting table
CREATE TABLE IF NOT EXISTS aachipsc_blog_rate_limits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    user_id INT NULL,
    action_type VARCHAR(50) NOT NULL, -- 'comment', 'vote', 'login'
    window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES aachipsc_blog_users(id) ON DELETE CASCADE,
    INDEX idx_ip_address (ip_address),
    INDEX idx_user_id (user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_window_start (window_start)
);

-- Insert some basic spam patterns
INSERT IGNORE INTO aachipsc_blog_spam_patterns (pattern, pattern_type, weight) VALUES
('viagra', 'keyword', 0.8),
('cialis', 'keyword', 0.8),
('casino', 'keyword', 0.6),
('lottery', 'keyword', 0.6),
('winner', 'keyword', 0.4),
('congratulations', 'keyword', 0.3),
('http[s]?://[^\s]+\.(tk|ml|ga|cf)', 'regex', 0.9),
('[0-9]{10,}', 'regex', 0.5);
