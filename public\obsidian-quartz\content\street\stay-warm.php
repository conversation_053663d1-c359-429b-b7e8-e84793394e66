<?php
// Auto-generated blog post
// Source: stay-warm.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'How to Stay Warm - Heartwarmers';
$meta_description = 'Hi I used to be homeless. Here are some ways to stay warm. Cotton is most common clothing material. It sucks! Especially if it gets wet. Cold goes right through.';
$meta_keywords = 'homeless, heartwarmers, writings, blog, advocacy, climate, disasterrelief, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../img/heartwarmer-logo.png';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'How to Stay Warm - Heartwarmers',
  'author' => 'A. A. Chips',
  'date' => '2018-01-28',
  'tags' => 
  array (
    0 => 'homeless',
    1 => 'heartwarmers',
    2 => 'writings',
    3 => 'blog',
    4 => 'advocacy',
    5 => 'climate',
    6 => 'disasterrelief',
  ),
  'excerpt' => 'Hi I used to be homeless. Here are some ways to stay warm. Cotton is most common clothing material. It sucks! Especially if it gets wet. Cold goes right through.',
  'categories' => 
  array (
    0 => 'Street Advocacy',
  ),
  'thumbnail' => '../../img/heartwarmer-logo.png',
  'source_file' => 'content\\street\\stay-warm.md',
);

// Raw content
$post_content = '<p>Hi I used to be homeless. Here are some ways to stay warm.</p>

<h3>Right layers</h3>

<p>Cotton is most common clothing material. It sucks! Especially if it gets wet. Cold goes right through. Do whatever you can to get some good thermal layers! Long john Top + Bottoms, socks, gloves, headgear, neck covering, jacket. Think polyester, wool. Can buy them nicely off big box stores. Good shoes. 100$ smart used is price of hotel room, or for winter gear. Lots of donations flow around churches charities and aid groups. Make it happen.</p>

<h3>Get a decent sleeping bag and keep it dry</h3>

<p>I used to get them from goodwill stacks. Way better than blankets. Lots of people have sleeping bags sitting in closets. Next best thing is bed comforters</p>

<h3>Secure a good emergency shelter for terrible weather nights and don’t burn the bridge</h3>

<p>If you can’t go to homeless shelters, try for a basement of someone you trust. Don’t steal shit! Be a good guest. Don’t tolerate abuse. You don’t have to give your body to someone offering shelter. Porches, under bridges.. Spending on a hotel room can work. Expect 100$ for a cheap motel room. Wind chill is NOT your friend.</p>

<h3>Figure some type of shelter out</h3>

<p>A tarp is better than nothing. A tent is better than a tarp. A tent and a tarp is better. A car or abandoned building is ideal. Sleeping in a doorway or on the sidewalk is a last resort. Having mace or a bat on hand in case you are woken up by a predator is a good idea.</p>

<h3>Sleep during the day</h3>

<p>It’s colder and less forgiving at night. If you can walk around and keep body heat in that time and sleep during the day you might save your butt from hypothermia.</p>

<h3>Eat food</h3>

<p>They are called calories for a reason.</p>

<h3>Avoid substances</h3>

<p>Alcohol and nicotine will make you colder, and give you the illusion of warmth. Beware.</p>

<h3>Milk open businesses</h3>

<p>Like fast food, public transit like buses and metro. Disclaimer: cover your face indoors with a mask. This isn’t a political science debate. Micro-spit is the enemy. If you are in super good shape and moving around all the time cause you don’t have housing, your metabolism may be awesome and you might not get really sick. Just do it to be a good guest. Look out for others so they can look out for you. Even if you are mad at people in warm houses for treating you like crap, just be the better person.</p>

<h3>Locate a printout guide to emergency warming shelters</h3>

<p>And even if you aren’t on the internet much, watch for code purples.</p>

<h3>Fill up on hot water</h3>

<p>Whenever you can. Some people just sip on hot coffee out of a disposable cup. What I did that I encourage others to do is get a reusable water bottle. Metal or nalgene is best. Can probably make due with an empty plastic bottle. Fill with scalding hot water at a gas station, coffee shop, restaurant, or wherever serves hot beverages. If you need to cover the bottle with a sock so the contact with skin wont give second degree burns. Keep this in your jacket or sleeping bag. Under insulation a large canteen can hold super warmth for 4-8 hours.</p>

<p>If you want to connect and talk about collecting and distributing hot water holding water bottles in your locality, find the group Heart Warmers on facebook. #HeartWarmers</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>