<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Switcher Test</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Header with Color Switcher -->
    <header>
        <div class="container header-container">
            <div class="site-branding">
                <img src="img/rosegrandpasvg.svg" alt="A. A. Chips" class="site-logo">
                <a href="#" class="site-title">A. A. Chips</a>
            </div>
            <nav class="nav">
                <ul>
                    <li><a href="#">Start Here</a></li>
                    <li><a href="#">Music Playlist</a></li>
                    <li><a href="#">Gallery</a></li>
                    <li><a href="#">Node Map</a></li>
                    <!-- Color switcher will be dynamically added here by JavaScript -->
                </ul>
            </nav>
        </div>
    </header>

    <div class="container" style="padding: 20px; max-width: 800px; margin: 2rem auto;">
        <h1>Color Switcher Test</h1>
        <p>Use the color switcher in the header to test the three themes: Light (☀️), Dark (🌙), and Gradient (🌈).</p>

        <div class="card">
            <div class="card-content">
                <h2>1. Link Color Test</h2>
                <p>This is a <a href="#" class="internal-link">test internal link</a> with background highlighting.</p>
                <p>This is a <a href="https://example.com" class="external-link">test external link</a> with border styling.</p>
                <p>This is a regular <a href="#">standard link</a> for comparison.</p>
            </div>
        </div>

        <div class="card">
            <div class="card-content">
                <h2>2. Card and Background Test</h2>
                <p>This card should change background colors and shadows based on the selected theme.</p>
                <blockquote>This is a blockquote to test text color variables.</blockquote>
                <code>This is inline code to test code background colors.</code>
            </div>
        </div>

        <div class="card">
            <div class="card-content">
                <h2>3. Category Card Test</h2>
                <div class="category-card" style="margin: 1rem 0;">
                    <h3>Sample Category</h3>
                    <p>This category card should demonstrate hover effects and color transitions in all themes.</p>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-content">
                <h2>4. Button Test</h2>
                <a href="#" class="btn">Primary Button</a>
                <a href="#" class="btn-secondary btn">Secondary Button</a>
            </div>
        </div>

        <div class="card">
            <div class="card-content">
                <h2>5. Theme Features</h2>
                <ul>
                    <li><strong>Light Theme:</strong> Clean, bright design with purple accents</li>
                    <li><strong>Dark Theme:</strong> Dark backgrounds with light text and adjusted colors</li>
                    <li><strong>Gradient Theme:</strong> Animated gradients with glassmorphism effects</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="js/color-switcher.js"></script>
</body>
</html>
