<?php
// Auto-generated blog post
// Source: more-than-a-meal.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'More Than a Meal - The power of sharing tables and street stages';
$meta_description = 'Exploring the power of sharing tables and street stages as a form of advocacy and community building.';
$meta_keywords = 'homelessness, advocacy, applechipkitchen, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://mountainx.com/wp-content/uploads/2016/11/Haywood-Street.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'More Than a Meal - The power of sharing tables and street stages',
  'author' => 'A. A. Chips',
  'date' => '2025-06-06',
  'tags' => 
  array (
    0 => 'homelessness',
    1 => 'advocacy',
    2 => 'applechipkitchen',
  ),
  'excerpt' => 'Exploring the power of sharing tables and street stages as a form of advocacy and community building.',
  'categories' => 
  array (
    0 => 'Street Advocacy',
  ),
  'thumbnail' => 'https://mountainx.com/wp-content/uploads/2016/11/Haywood-Street.jpg',
  'source_file' => 'content\\street\\more-than-a-meal.md',
);

// Raw content
$post_content = '<h3>Beyond Charity: Companionship at Free Meal Programs</h3>

<p>It’s easy to see free meal programs as simply a place to receive food. But for those who frequent them, they are often vital community hubs. As someone with privilege, you have a unique opportunity to engage with these spaces not just as a giver, but as a companion.</p>

<p>Imagine sitting down not to serve, but to share a meal. To listen to the stories of your neighbors, to build genuine friendships, and to understand their needs firsthand. This isn\'t about charity; it\'s about solidarity. It\'s about recognizing our shared humanity and breaking down the stigmas associated with poverty and aid.</p>

<p>Think of yourself as a "gift fairy" or "guardian angel," subtly learning about simple needs and finding ways to fulfill them in collaboration with other allies. By eating together – hand in hand, not hand out – we can foster a deeper understanding and work towards dismantling the systems that create inequality.</p>

<h3>The Vitality of Buskers: More Than Just Street Performers</h3>

<p>Walk through any vibrant city and you’re likely to encounter the sounds and sights of buskers – musicians, artists, and craftspeople sharing their talents on the streets. These individuals are more than just entertainment; they are a vital part of the local tourism economy and the cultural fabric of our communities.</p>

<p>Unfortunately, some cities are pushing for policies that criminalize both homelessness and busking. This approach punishes individuals for the very visible signs of systemic issues. A more humane and effective solution lies in addressing the root causes of destitution.</p>

<p>Instead of banning buskers, let\'s provide them with the venues, permits, and dedicated spaces they need to operate legitimately. By supporting these artists, we enrich our public spaces and recognize their valuable contributions. Similarly, let\'s focus on lifting people out of homelessness by providing access to public showers, ensuring food security through incentivized food donation programs, and prioritizing low-income housing.</p>

<p>By shifting our focus from criminalization to genuine support and collaboration, we can create more inclusive and vibrant communities where everyone has the opportunity to thrive, whether sharing a meal or sharing their art.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>