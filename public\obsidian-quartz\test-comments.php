<?php
/**
 * Test page for comment system
 */

// Load path helper and configuration
require_once __DIR__ . '/path-helper.php';
$config = include __DIR__ . '/config.php';
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Comment System Test';
$meta_description = 'Testing the comment system functionality';
$meta_keywords = 'test, comments, system';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];

// Mock post data for testing
$post_data = [
    'title' => 'Comment System Test',
    'author' => 'A. A. Chips',
    'date' => date('Y-m-d'),
    'excerpt' => 'This is a test page for the comment system.'
];

// Generate content
ob_start();
?>
<article class="post-header">
    <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    
    <div class="post-meta">
        <span class="post-author"><i class="icon-user"></i>By <?php echo htmlspecialchars($post_data['author']); ?></span>
        <span class="meta-separator"> • </span>
        <span class="post-date"><i class="icon-calendar"></i><?php echo date('F j, Y', strtotime($post_data['date'])); ?></span>
    </div>
</article>

<div class="post-content">
    <h2>Comment System Test Page</h2>
    
    <p>This is a test page to verify that the comment system is working correctly. You should see a comment section below this content.</p>
    
    <h3>Features to Test:</h3>
    <ul>
        <li><strong>Google Authentication:</strong> Click "Sign in with Google" to authenticate</li>
        <li><strong>Post Comments:</strong> Write and submit a comment</li>
        <li><strong>Reply to Comments:</strong> Click "Reply" on any comment</li>
        <li><strong>Vote on Comments:</strong> Use the like/dislike buttons</li>
        <li><strong>Real-time Updates:</strong> Comments should appear immediately</li>
    </ul>
    
    <h3>Security Features:</h3>
    <ul>
        <li><strong>Spam Detection:</strong> Try posting spam-like content</li>
        <li><strong>Rate Limiting:</strong> Try posting many comments quickly</li>
        <li><strong>Input Validation:</strong> Try posting empty or very long comments</li>
    </ul>
    
    <div class="alert alert-info">
        <strong>Note:</strong> This is a test page. Comments posted here are for testing purposes only.
    </div>
</div>

<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>
