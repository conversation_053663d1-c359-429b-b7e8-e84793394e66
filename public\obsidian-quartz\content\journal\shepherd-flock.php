<?php
// Auto-generated blog post
// Source: shepherd-flock.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Why the Shepherd had to leave the flock behind - for the one who was left behind';
$meta_description = 'There’s a story about a shepherd with a hundred sheep. One goes missing, so he leaves the ninety-nine to search for it. When he finds it, he carries it home, rejoicing. But no one asks the ninety-nine how they felt. If you were one of them—if you watched the shepherd walk away and wondered, "Why wasn’t I enough?"—this is for you.';
$meta_keywords = 'writings, personal, journal, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Why the Shepherd had to leave the flock behind - for the one who was left behind',
  'author' => 'A. A. Chips',
  'date' => '2025-04-15',
  'excerpt' => 'There’s a story about a shepherd with a hundred sheep. One goes missing, so he leaves the ninety-nine to search for it. When he finds it, he carries it home, rejoicing. But no one asks the ninety-nine how they felt. If you were one of them—if you watched the shepherd walk away and wondered, "Why wasn’t I enough?"—this is for you.',
  'tags' => 
  array (
    0 => 'writings',
    1 => 'personal',
    2 => 'journal',
  ),
  'source_file' => 'content\\journal\\shepherd-flock.md',
);

// Raw content
$post_content = '<p>There’s a story about a shepherd with a hundred sheep. One goes missing, so he leaves the ninety-nine to search for it. When he finds it, he carries it home, rejoicing.</p>

<p><strong>But no one asks the ninety-nine how they felt.</strong></p>

<p>If you were one of them—if you watched the shepherd walk away and wondered, _"Why wasn’t I enough?"_—this is for you.</p>

<h3><strong>The Part of the Story No One Tells</strong></h3>

<p>The parable doesn’t say what the ninety-nine _saw_ from their hillside. Maybe they didn’t notice the cracks in the ground beneath them. Maybe they didn’t know the shepherd’s legs were already buckling from holding up the weight of a flock that was slowly sinking.</p>

<p>He didn’t leave because he loved them less.</p>
<p>He left because <strong>staying would have meant watching them all drown</strong>.</p>

<h3><strong>To the One Who Was Left</strong></h3>

<p>I left in 2017. Not as a shepherd, but as someone who was breaking—watching a child I loved be handed to harm, feeling my own feet slipping deeper into mud with every year that passed. I was a vagabond: kind, but unstable; loved, but not _safe_.</p>

<p>When I ran, it wasn’t from _you_.</p>
<p>It was from the version of me that could never have been strong enough to help.</p>

<p>You were never supposed to carry this. You were a child, and my leaving wasn’t a choice _against you_—it was a scream toward a future where I could finally stand on solid ground. Where I could be someone worth trusting.</p>

<p>But here’s what the parable doesn’t say:</p>

<ul><li><strong>The shepherd didn’t leave the ninety-nine in danger.</strong> He left them where they _believed_ they were safe. (Sometimes the ones who stay can’t see the cracks yet.)</li>

<p><li><strong>The return matters more than the leaving.</strong> The story ends with celebration because the shepherd isn’t just bringing back the lost sheep—he’s coming back _different_. Stronger. Able to bear weight.</li></p>

<p><li><strong>You get to be angry.</strong> The ninety-nine had every right to feel abandoned. Love doesn’t erase that. But love _can_ outlive the leaving.</li></ul></p>


<h3><strong>A Letter to the One I Left</strong></h3>

<p>_I’m sorry you were caught in the middle.</p>
<p>I’m sorry my absence became a wound.</p>
<p>I couldn’t fix the brokenness around you then.</p>
<p>But I am here now—not as the vagabond you knew,</p>
<p>but as someone who has learned how to build a home.</p>
<p>If you ever want to see it, the door is open.</p>
<p>No pressure. No demands.</p>
<p>Just a quiet space where you are always welcome._</p>

<h3><strong>For Anyone Who Walked Away to Survive</strong></h3>

<p>You don’t owe the past your future. But if there are people you love still standing on that hillside, wondering why you left, remember: <strong>sometimes the only way to save anyone is to first save yourself</strong>.</p>

<p>The parable never blamed the ninety-nine for not understanding.</p>
<p>It just asked them to trust that the shepherd’s love was wider than the leaving.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>