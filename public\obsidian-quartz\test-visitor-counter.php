<?php
/**
 * Visitor Counter Test Page
 * For A. A. Chips' Obsidian-Quartz Blog
 * 
 * This page tests the visitor counter functionality and displays
 * different counter styles for demonstration.
 */

// Initialize page variables
$page_title = 'Visitor Counter Test';
$meta_description = 'Testing the visitor counter functionality for A. A. Chips\' blog';
$meta_keywords = 'visitor counter, test, analytics, blog';

// Initialize path constants
require_once 'path-helper.php';
$config = include 'config.php';
$paths = initPaths($config, __FILE__);

// Set fallback variables for backward compatibility
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];

// Start output buffering for content
ob_start();
?>

<div class="post-content">
    <div class="post-header">
        <h1>Visitor Counter Test Page</h1>
        <div class="post-meta">
            <span class="post-date">Test Page</span>
            <span class="post-author"><PERSON><PERSON> <PERSON><PERSON></span>
        </div>
    </div>

    <div class="content">
        <h2>Welcome to the Visitor Counter Test!</h2>
        
        <p>This page demonstrates the visitor counter functionality for the obsidian-quartz blog system. 
        The counter tracks both page-specific visits and site-wide statistics with proper deduplication 
        to prevent inflated counts.</p>

        <h3>How It Works</h3>
        <ul>
            <li><strong>Unique Daily Visits:</strong> Only counts one visit per IP address per day per page</li>
            <li><strong>Site-wide Tracking:</strong> Maintains total visit counts across all pages</li>
            <li><strong>Performance Optimized:</strong> Uses aggregated statistics for fast display</li>
            <li><strong>Privacy Friendly:</strong> Stores hashed user agent data, not personal information</li>
        </ul>

        <h3>Counter Styles</h3>
        <p>The visitor counter supports multiple display styles:</p>

        <h4>1. Default Retro Style</h4>
        <p>This appears in the footer of every page with a classic green-on-black LCD look.</p>

        <h4>2. Retro LCD Counter</h4>
        <?php
        // Include visitor counter display
        require_once 'visitor-counter/visitor-display.php';
        
        echo displayRetroCounter('test-visitor-counter', [
            'digits' => 6,
            'style' => 'lcd',
            'counter_type' => 'page',
            'show_label' => true
        ]);
        ?>

        <h4>3. Retro LED Counter (Red)</h4>
        <?php
        echo displayRetroCounter('test-visitor-counter', [
            'digits' => 6,
            'style' => 'led',
            'counter_type' => 'site',
            'show_label' => true
        ]);
        ?>

        <h4>4. Mechanical Counter Style</h4>
        <?php
        echo displayRetroCounter('test-visitor-counter', [
            'digits' => 6,
            'style' => 'mechanical',
            'counter_type' => 'page',
            'show_label' => true
        ]);
        ?>

        <h4>5. Modern Style</h4>
        <?php
        echo displayVisitorCounter('test-visitor-counter', 'Visitor Counter Test', [
            'style' => 'modern',
            'show_page_count' => true,
            'show_site_count' => true,
            'show_today_count' => true,
            'format' => 'full',
            'track_visit' => false // Don't track multiple visits on same page
        ]);
        ?>

        <h4>6. Minimal Style</h4>
        <?php
        echo displayVisitorCounter('test-visitor-counter', 'Visitor Counter Test', [
            'style' => 'minimal',
            'show_page_count' => true,
            'show_site_count' => true,
            'show_today_count' => false,
            'format' => 'full',
            'track_visit' => false
        ]);
        ?>

        <h4>7. Compact Display</h4>
        <?php
        echo displayVisitorCounter('test-visitor-counter', 'Visitor Counter Test', [
            'style' => 'retro',
            'show_page_count' => true,
            'show_site_count' => true,
            'format' => 'compact',
            'track_visit' => false
        ]);
        ?>

        <h3>Current Statistics</h3>
        <?php
        // Get and display current visitor data
        $visitorData = getVisitorCountData('test-visitor-counter');
        if (!empty($visitorData)) {
            echo '<div class="visitor-stats">';
            echo '<h4>Raw Data:</h4>';
            echo '<ul>';
            echo '<li>Page Visits: ' . number_format($visitorData['page_visits']) . '</li>';
            echo '<li>Page Unique Visits: ' . number_format($visitorData['page_unique_visits']) . '</li>';
            echo '<li>Page Today Visits: ' . number_format($visitorData['page_today_visits']) . '</li>';
            echo '<li>Site Total Visits: ' . number_format($visitorData['site_total_visits']) . '</li>';
            echo '<li>Site Today Visits: ' . number_format($visitorData['site_today_visits']) . '</li>';
            echo '<li>Last Updated: ' . $visitorData['last_updated'] . '</li>';
            echo '</ul>';
            echo '</div>';
        }
        ?>

        <h3>Testing Instructions</h3>
        <ol>
            <li>Refresh this page to see if the counter increments (it should only increment once per day per IP)</li>
            <li>Visit other pages on the site to see site-wide counter increase</li>
            <li>Check the footer of any page to see the default counter display</li>
            <li>Try visiting from different devices/networks to see unique visitor tracking</li>
        </ol>

        <h3>Customization</h3>
        <p>You can customize the visitor counter by:</p>
        <ul>
            <li>Editing <code>visitor-counter/visitor-display.php</code> for display options</li>
            <li>Modifying <code>css/visitor-counter.css</code> for styling</li>
            <li>Updating <code>includes/footer.php</code> to change footer display</li>
            <li>Using different style options: 'retro', 'modern', 'minimal'</li>
        </ul>

        <div class="test-info">
            <h4>Technical Details</h4>
            <p><strong>Page Slug:</strong> test-visitor-counter</p>
            <p><strong>Your IP:</strong> <?php echo $_SERVER['REMOTE_ADDR'] ?? 'Unknown'; ?></p>
            <p><strong>User Agent Hash:</strong> <?php echo substr(hash('sha256', $_SERVER['HTTP_USER_AGENT'] ?? ''), 0, 16); ?>...</p>
            <p><strong>Session ID:</strong> <?php echo session_id() ?: 'None'; ?></p>
            <p><strong>Current Date:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// Include the page template
include 'page template.htm';
?>

<style>
/* Additional styles for test page */
.visitor-stats {
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 16px;
    margin: 16px 0;
}

.visitor-stats h4 {
    margin-top: 0;
    color: #333;
}

.visitor-stats ul {
    margin: 8px 0;
    padding-left: 20px;
}

.test-info {
    background: #e8f4f8;
    border: 1px solid #bee5eb;
    border-radius: 6px;
    padding: 16px;
    margin: 20px 0;
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

.test-info h4 {
    margin-top: 0;
    color: #0c5460;
}

.test-info p {
    margin: 4px 0;
    color: #0c5460;
}

/* Space out the different counter examples */
.retro-counter,
.visitor-counter {
    margin: 20px 0;
}
</style>
