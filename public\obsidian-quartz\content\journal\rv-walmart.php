<?php
// Auto-generated blog post
// Source: rv-walmart.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Waking up with frozen feet in the RV at Walmart - January 20th, 2016';
$meta_description = 'Six AM. Freezing feet. Woke up in a cold stupor. Sixteen degrees with three inches of ice outside. <PERSON><PERSON><PERSON> woke up too. I was ride-sharing in his RV to North Carolina with his son. We maybe only got three hours of sleep on the road, but definitely \'rejuvenated\'. He tried the RV heater, but the fuse blew last night. Hence waking up freezing. Luckily, we were parked at a Walmart in Johnson City, having tried to stay south of the snowstorm. The lot was on a huge hill, and we weren\'t getting out until it warmed up. We weren\'t the only ones stranded, and while the company wasn\'t great, they seemed well-intentioned.';
$meta_keywords = 'journal, april, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../img/self/kairv.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Waking up with frozen feet in the RV at Walmart - January 20th, 2016',
  'date' => '2016-01-20',
  'tags' => 
  array (
    0 => 'journal',
    1 => 'april',
  ),
  'categories' => 
  array (
    0 => 'Journal',
  ),
  'excerpt' => 'Six AM. Freezing feet. Woke up in a cold stupor. Sixteen degrees with three inches of ice outside. Gumbo woke up too. I was ride-sharing in his RV to North Carolina with his son. We maybe only got three hours of sleep on the road, but definitely \'rejuvenated\'. He tried the RV heater, but the fuse blew last night. Hence waking up freezing. Luckily, we were parked at a Walmart in Johnson City, having tried to stay south of the snowstorm. The lot was on a huge hill, and we weren\'t getting out until it warmed up. We weren\'t the only ones stranded, and while the company wasn\'t great, they seemed well-intentioned.',
  'thumbnail' => '../../img/self/kairv.jpg',
  'source_file' => 'content\\journal\\rv-walmart.md',
);

// Raw content
$post_content = '<h2>January 20th, 2016.   </h2>

<p>Find image</p>

<p>Six AM. Freezing feet. Woke up in a cold stupor. Sixteen degrees with three inches of ice outside. Gumbo woke up too. I was ride-sharing in his RV to North Carolina with his son. We maybe only got three hours of sleep on the road, but definitely \'rejuvenated\'. He tried the RV heater, but the fuse blew last night. Hence waking up freezing. Luckily, we were parked at a Walmart in Johnson City, having tried to stay south of the snowstorm. The lot was on a huge hill, and we weren\'t getting out until it warmed up. We weren\'t the only ones stranded, and while the company wasn\'t great, they seemed well-intentioned.</p>

<p>After finishing the LA to DC trip in late 2014, I was sure I wouldn\'t fall back into the Matrix. I didn\'t want to consume. Bucket baths and dumpster diving were my way of life. Employment felt like the planet\'s cancer. That winter, I was kicked out and living in my car. Someone from a 350 meeting took me in, and I still consider her my adoptive mother. She asked for nothing in return, but I struggled to accept her kindness, feeling I didn\'t deserve or need it. Family consistently tried to discourage me, wanting me to return to my old ways. I had a deep lack of self-love. They never wanted me to be where I am now.</p>

<p>When I lived out of my car spanning multiple winters, my Nalgene water bottle was one of the greatest survival tools against the cold. I could regularly get it filled with hot water at coffee shops and use it to radiate heat in my pockets or wherever I needed warmth. It helped me sleep comfortably in temperatures as low as 20-30 degrees. This winter, I want to collect donated Nalgene bottles to distribute for free to the houseless population in Washington DC who will struggle to stay warm.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>