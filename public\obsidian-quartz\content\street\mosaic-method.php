<?php
// Auto-generated blog post
// Source: mosaic-method.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'The MOSAIC Method - A Free Assessment Tool';
$meta_description = 'I wanted to share one of my favorite free assessment tools available on the internet. It\'s called the MOSAIC Method. It was developed by <PERSON>, author of "The Gift of Fear." Gavin and the MOSAIC Method have been featured on Oprah. This is a tool used by large corporations and the Secret Service to evaluate the danger of specific high-risk situations and people.';
$meta_keywords = 'safety, assessment, MOSAIC, Gavin De Becker, Gift of Fear, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://openmindmatters.com/wp-content/uploads/2019/02/iStock-487729465.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'The MOSAIC Method - A Free Assessment Tool',
  'author' => 'A. A. Chips',
  'date' => '2020-06-26',
  'tags' => 
  array (
    0 => 'safety',
    1 => 'assessment',
    2 => 'MOSAIC',
    3 => 'Gavin De Becker',
    4 => 'Gift of Fear',
  ),
  'excerpt' => 'I wanted to share one of my favorite free assessment tools available on the internet. It\'s called the MOSAIC Method. It was developed by Gavin De Becker, author of "The Gift of Fear." Gavin and the MOSAIC Method have been featured on Oprah. This is a tool used by large corporations and the Secret Service to evaluate the danger of specific high-risk situations and people.',
  'categories' => 
  array (
    0 => 'Inspiration',
    1 => 'Advocacy',
    2 => 'Resources',
  ),
  'thumbnail' => 'https://openmindmatters.com/wp-content/uploads/2019/02/iStock-487729465.jpg',
  'source_file' => 'content\\street\\mosaic-method.md',
);

// Raw content
$post_content = '<p>I wanted to share one of my favorite free assessment tools available on the internet. It\'s called the MOSAIC Method. It was developed by Gavin De Becker, author of "The Gift of Fear." Gavin and the MOSAIC Method have been featured on Oprah. This is a tool used by large corporations and the Secret Service to evaluate the danger of specific high-risk situations and people. The assessment has several free public service versions aimed at people who may be in a dangerous relationship, or have a client/coworker who might pose a threat risk, or if someone has a stalker and they want to evaluate the situation objectively.</p>

<p><a href="https://www.mosaicmethod.com/" class="external-link">https://www.mosaicmethod.com/</a></p>

<p>>MOSAIC is an error avoidance method, a computer-assisted method for conducting comprehensive assessments - in the same way that diagnosis is a method used by a doctor. An effective medical diagnosis results when a doctor knows which questions to ask, knows which tests will produce the most accurate answers, and then knows how to draw relevant conclusions from all the answers combined together.</p>
<p>></p>
<p>>Similarly, assessing whether a situation has the combination of factors that are associated with escalated risk and danger requires that you know what questions to ask, and then know how to consider all your answers in a way that enhances insight. The MOSAIC method works by breaking a situation down to its elements, factor-by-factor, and then seeing what picture emerges when the pieces of the puzzle are put together.</p>
<p>></p>
<p>>MOSAIC helps the assessor weigh the present situation in light of expert opinion and research, and instantly compare the present situation to past cases where the outcomes are known.</p>

<p>There are so many instances where you or someone you love or work with, may be in a questionable and possibly dangerous situation of violence. Having this tool in your pocket can come in handy working with clients who may be at increased risk of being trapped in domestic violence, or hostile work school and living environments. Last week I mentioned how with I-statements, one of the parallel principles is Observation over Evaluation. Sometimes our intuition tells us what we need to know to stay safe. Sometimes trauma causes us to misfire and perceive danger where there is none. Having a tool to break that down available at any given time is great and sometimes essential for trauma survivors.</p>

<iframe width="560" height="315" src="https://www.youtube.com/embed/OIMXbFWE_jA" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>
<p>Preventing Targeted Violence: Secret Service National Threat Assessment Center</p>
<p>by U.S. Secret Service</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>